import { NextRequest, NextResponse } from 'next/server';
import { UserService } from '@/lib/database';
import { z } from 'zod';

// 验证管理员权限的中间件函数
async function verifyAdmin(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader) {
    return null;
  }

  // 这里简化处理，实际应该验证JWT token
  // 暂时通过请求头中的用户信息验证
  try {
    const userInfo = JSON.parse(authHeader.replace('Bearer ', ''));
    if (userInfo.role !== 'admin') {
      return null;
    }
    return userInfo;
  } catch {
    return null;
  }
}

// 获取所有用户
export async function GET(request: NextRequest) {
  try {
    const admin = await verifyAdmin(request);
    if (!admin) {
      return NextResponse.json(
        { success: false, error: '权限不足' },
        { status: 403 }
      );
    }

    const users = await UserService.getAllUsers();
    
    // 移除敏感信息
    const safeUsers = users.map(({ password_hash: _, ...user }) => user);

    return NextResponse.json({
      success: true,
      data: safeUsers,
    });

  } catch (error) {
    console.error('获取用户列表错误:', error);
    return NextResponse.json(
      { success: false, error: '获取用户列表失败' },
      { status: 500 }
    );
  }
}

const createUserSchema = z.object({
  username: z.string().min(3, '用户名至少3个字符').max(50, '用户名最多50个字符'),
  email: z.string().email('邮箱格式不正确'),
  password: z.string().min(6, '密码至少6个字符'),
  role: z.enum(['admin', 'user']),
  auth_code: z.string().optional(),
});

// 创建用户
export async function POST(request: NextRequest) {
  try {
    const admin = await verifyAdmin(request);
    if (!admin) {
      return NextResponse.json(
        { success: false, error: '权限不足' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const userData = createUserSchema.parse(body);

    const user = await UserService.createUser(userData);
    
    // 移除敏感信息
    const { password_hash: _, ...userInfo } = user;

    return NextResponse.json({
      success: true,
      data: userInfo,
      message: '用户创建成功',
    });

  } catch (error: unknown) {
    console.error('创建用户错误:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      );
    }

    // 处理数据库唯一约束错误
    if (error && typeof error === 'object' && 'code' in error && error.code === '23505') {
      const dbError = error as { constraint?: string };
      if (dbError.constraint?.includes('username')) {
        return NextResponse.json(
          { success: false, error: '用户名已存在' },
          { status: 409 }
        );
      }
      if (dbError.constraint?.includes('email')) {
        return NextResponse.json(
          { success: false, error: '邮箱已被注册' },
          { status: 409 }
        );
      }
    }

    return NextResponse.json(
      { success: false, error: '创建用户失败' },
      { status: 500 }
    );
  }
}
