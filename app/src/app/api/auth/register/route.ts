import { NextRequest, NextResponse } from 'next/server';
import { UserService } from '@/lib/database';
import { z } from 'zod';

const registerSchema = z.object({
  username: z.string().min(3, '用户名至少3个字符').max(50, '用户名最多50个字符'),
  email: z.string().email('邮箱格式不正确'),
  password: z.string().min(6, '密码至少6个字符'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword'],
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 验证请求数据
    const { username, email, password } = registerSchema.parse(body);

    // 创建用户
    const user = await UserService.createUser({
      username,
      email,
      password,
      role: 'user',
    });

    // 移除敏感信息
    const { password_hash: _, ...userInfo } = user;

    return NextResponse.json({
      success: true,
      data: userInfo,
      message: '注册成功',
    });

  } catch (error: unknown) {
    console.error('注册错误:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      );
    }

    // 处理数据库唯一约束错误
    if (error && typeof error === 'object' && 'code' in error && error.code === '23505') {
      const dbError = error as { constraint?: string };
      if (dbError.constraint?.includes('username')) {
        return NextResponse.json(
          { success: false, error: '用户名已存在' },
          { status: 409 }
        );
      }
      if (dbError.constraint?.includes('email')) {
        return NextResponse.json(
          { success: false, error: '邮箱已被注册' },
          { status: 409 }
        );
      }
    }

    return NextResponse.json(
      { success: false, error: '注册失败，请稍后重试' },
      { status: 500 }
    );
  }
}
