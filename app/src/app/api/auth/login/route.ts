import { NextRequest, NextResponse } from 'next/server';
import { UserService } from '@/lib/database';
import { z } from 'zod';

const loginSchema = z.object({
  username: z.string().min(1, '用户名不能为空'),
  password: z.string().min(1, '密码不能为空'),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 验证请求数据
    const { username, password } = loginSchema.parse(body);

    // 验证用户
    const user = await UserService.validateUser(username, password);
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: '用户名或密码错误' },
        { status: 401 }
      );
    }

    // 移除敏感信息
    const { password_hash: _, ...userInfo } = user;

    return NextResponse.json({
      success: true,
      data: userInfo,
      message: '登录成功',
    });

  } catch (error: unknown) {
    console.error('登录错误:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: '登录失败，请稍后重试' },
      { status: 500 }
    );
  }
}
