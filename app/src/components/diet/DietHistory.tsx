'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import axios from 'axios';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Trash2, Clock, Utensils, Loader2, RefreshCw } from 'lucide-react';
import { useAuthStore } from '@/store/useAuthStore';
import { DietRecord } from '@/types';

export function DietHistory() {
  const [records, setRecords] = useState<DietRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [recordToDelete, setRecordToDelete] = useState<DietRecord | null>(null);
  const [deleting, setDeleting] = useState(false);
  
  const { user } = useAuthStore();

  // 获取饮食记录
  const fetchRecords = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const response = await axios.get('/api/diet/records', {
        headers: {
          'Authorization': `Bearer ${JSON.stringify(user)}`,
        },
      });

      if (response.data.success) {
        setRecords(response.data.data);
      } else {
        toast.error('获取历史记录失败');
      }
    } catch (error: unknown) {
      console.error('获取历史记录错误:', error);
      const axiosError = error as { response?: { data?: { error?: string } } };
      toast.error(axiosError.response?.data?.error || '获取历史记录失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除记录
  const handleDelete = async () => {
    if (!recordToDelete || !user) return;

    try {
      setDeleting(true);
      const response = await axios.delete(`/api/diet/records/${recordToDelete.id}`, {
        headers: {
          'Authorization': `Bearer ${JSON.stringify(user)}`,
        },
      });

      if (response.data.success) {
        toast.success('记录删除成功');
        setRecords(records.filter(record => record.id !== recordToDelete.id));
        setDeleteDialogOpen(false);
        setRecordToDelete(null);
      } else {
        toast.error('删除失败');
      }
    } catch (error: unknown) {
      console.error('删除记录错误:', error);
      const axiosError = error as { response?: { data?: { error?: string } } };
      toast.error(axiosError.response?.data?.error || '删除失败');
    } finally {
      setDeleting(false);
    }
  };

  // 获取餐次显示名称
  const getMealTypeName = (mealType: string) => {
    const names = {
      breakfast: '早餐',
      lunch: '午餐',
      dinner: '晚餐',
      snack: '加餐',
    };
    return names[mealType as keyof typeof names] || mealType;
  };



  useEffect(() => {
    fetchRecords();
  }, [user]); // eslint-disable-line react-hooks/exhaustive-deps

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin mr-2" />
          加载中...
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">饮食历史</h2>
          <p className="text-gray-600">查看您的饮食记录历史</p>
        </div>
        <Button
          variant="outline"
          onClick={fetchRecords}
          disabled={loading}
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          刷新
        </Button>
      </div>

      {records.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <Utensils className="w-12 h-12 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500">还没有饮食记录</p>
            <p className="text-sm text-gray-400 mt-2">开始记录您的第一餐吧！</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {records.map((record) => (
            <Card key={record.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-600">
                      {format(new Date(record.record_time), 'yyyy年MM月dd日 HH:mm', { locale: zhCN })}
                    </span>
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                      {getMealTypeName(record.meal_type)}
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setRecordToDelete(record);
                      setDeleteDialogOpen(true);
                    }}
                  >
                    <Trash2 className="w-4 h-4 text-red-500" />
                  </Button>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* 原始输入 */}
                <div>
                  <h4 className="font-medium text-sm text-gray-700 mb-2">原始输入</h4>
                  <div className="flex flex-col sm:flex-row sm:items-start space-y-2 sm:space-y-0 sm:space-x-3">
                    {record.input_type === 'image' && record.image_url && (
                      <img
                        src={record.image_url}
                        alt="食物图片"
                        className="w-16 h-16 sm:w-20 sm:h-20 rounded-lg object-cover mx-auto sm:mx-0"
                      />
                    )}
                    <p className="text-sm text-gray-600 flex-1 text-center sm:text-left">{record.original_input}</p>
                  </div>
                </div>

                {/* 食材清单 */}
                {record.ingredients && record.ingredients.length > 0 && (
                  <div>
                    <h4 className="font-medium text-sm text-gray-700 mb-2">食材清单</h4>
                    <div className="flex flex-wrap gap-2">
                      {record.ingredients.map((ingredient, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full"
                        >
                          {ingredient.name} {ingredient.amount}{ingredient.unit}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* 菜品清单 */}
                {record.dishes && record.dishes.length > 0 && (
                  <div>
                    <h4 className="font-medium text-sm text-gray-700 mb-2">菜品清单</h4>
                    <div className="flex flex-wrap gap-2">
                      {record.dishes.map((dish, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full"
                        >
                          {dish}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* 热量和分析 */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between pt-2 border-t space-y-2 sm:space-y-0">
                  <div className="flex items-center justify-center sm:justify-start space-x-4">
                    <span className="text-lg font-semibold text-red-600">
                      {record.total_calories} 卡路里
                    </span>
                  </div>
                  {record.ai_analysis && (
                    <p className="text-sm text-gray-600 max-w-md text-center sm:text-right">
                      {record.ai_analysis}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* 删除确认对话框 */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              您确定要删除这条饮食记录吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
              disabled={deleting}
            >
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={deleting}
            >
              {deleting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  删除中...
                </>
              ) : (
                '确认删除'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
