import { supabase, supabaseAdmin, TABLES, isConfigured } from './supabase';
import { User, UserSettings, DietRecord, Ingredient, Dish } from '@/types';
import bcrypt from 'bcryptjs';

// 用户相关操作
export class UserService {
  // 创建用户
  static async createUser(userData: {
    username: string;
    email: string;
    password: string;
    role?: 'admin' | 'user';
    auth_code?: string;
  }): Promise<User> {
    if (!isConfigured) {
      throw new Error('数据库未配置，请设置正确的环境变量');
    }
    const passwordHash = await bcrypt.hash(userData.password, 12);

    const { data, error } = await supabaseAdmin
      .from(TABLES.USERS)
      .insert({
        username: userData.username,
        email: userData.email,
        password_hash: passwordHash,
        role: userData.role || 'user',
        auth_code: userData.auth_code,
      })
      .select('id, username, email, role, auth_code, created_at, updated_at')
      .single();

    if (error) throw error;
    return data;
  }

  // 验证用户登录
  static async validateUser(username: string, password: string): Promise<User | null> {
    const { data, error } = await supabaseAdmin
      .from(TABLES.USERS)
      .select('*')
      .eq('username', username)
      .single();

    if (error || !data) return null;

    const isValid = await bcrypt.compare(password, data.password_hash);
    if (!isValid) return null;

    // 返回不包含密码的用户信息
    const { password_hash: _, ...userInfo } = data;
    return userInfo as User;
  }

  // 获取用户信息
  static async getUserById(id: string): Promise<User | null> {
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .select('id, username, email, role, auth_code, created_at, updated_at')
      .eq('id', id)
      .single();

    if (error) return null;
    return data;
  }

  // 获取所有用户（管理员功能）
  static async getAllUsers(): Promise<User[]> {
    const { data, error } = await supabaseAdmin
      .from(TABLES.USERS)
      .select('id, username, email, role, auth_code, created_at, updated_at')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  // 更新用户信息
  static async updateUser(id: string, updates: Partial<User>): Promise<User> {
    const { data, error } = await supabaseAdmin
      .from(TABLES.USERS)
      .update(updates)
      .eq('id', id)
      .select('id, username, email, role, auth_code, created_at, updated_at')
      .single();

    if (error) throw error;
    return data;
  }

  // 重置用户密码
  static async resetPassword(id: string, newPassword: string): Promise<void> {
    const passwordHash = await bcrypt.hash(newPassword, 12);
    
    const { error } = await supabaseAdmin
      .from(TABLES.USERS)
      .update({ password_hash: passwordHash })
      .eq('id', id);

    if (error) throw error;
  }

  // 删除用户
  static async deleteUser(id: string): Promise<void> {
    const { error } = await supabaseAdmin
      .from(TABLES.USERS)
      .delete()
      .eq('id', id);

    if (error) throw error;
  }
}

// 用户设置相关操作
export class UserSettingsService {
  // 获取用户设置
  static async getUserSettings(userId: string): Promise<UserSettings | null> {
    const { data, error } = await supabase
      .from(TABLES.USER_SETTINGS)
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      // 如果没有设置记录，创建默认设置
      if (error.code === 'PGRST116') {
        const defaultSettings = {
          user_id: userId,
          ai_model: 'gpt-4o',
          api_base_url: process.env.DEFAULT_AI_BASE_URL || 'https://api.openai.com',
          api_key: process.env.DEFAULT_AI_API_KEY || '',
        };

        const { data: newSettings, error: createError } = await supabase
          .from(TABLES.USER_SETTINGS)
          .insert(defaultSettings)
          .select()
          .single();

        if (createError) return null;
        return newSettings;
      }
      return null;
    }
    return data;
  }

  // 更新用户设置
  static async updateUserSettings(userId: string, settings: Partial<UserSettings>): Promise<UserSettings> {
    // 先检查是否存在设置记录
    const { data: existingSettings } = await supabase
      .from(TABLES.USER_SETTINGS)
      .select('id')
      .eq('user_id', userId)
      .single();

    if (existingSettings) {
      // 如果存在，则更新
      const { data, error } = await supabase
        .from(TABLES.USER_SETTINGS)
        .update(settings)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } else {
      // 如果不存在，则插入
      const { data, error } = await supabase
        .from(TABLES.USER_SETTINGS)
        .insert({
          user_id: userId,
          ...settings,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    }
  }
}

// 饮食记录相关操作
export class DietRecordService {
  // 创建饮食记录
  static async createDietRecord(recordData: {
    user_id: string;
    original_input: string;
    input_type: 'text' | 'image';
    image_url?: string;
    meal_type: string;
    record_time?: string;
    total_calories: number;
    ai_analysis: string;
    dishes: string[];
    ingredients: Omit<Ingredient, 'id' | 'diet_record_id' | 'created_at'>[];
  }): Promise<DietRecord> {
    // 创建饮食记录
    const { data: record, error: recordError } = await supabase
      .from(TABLES.DIET_RECORDS)
      .insert({
        user_id: recordData.user_id,
        original_input: recordData.original_input,
        input_type: recordData.input_type,
        image_url: recordData.image_url,
        meal_type: recordData.meal_type,
        record_time: recordData.record_time,
        total_calories: recordData.total_calories,
        ai_analysis: recordData.ai_analysis,
        dishes: recordData.dishes,
      })
      .select()
      .single();

    if (recordError) throw recordError;

    // 创建食材记录
    if (recordData.ingredients.length > 0) {
      const ingredientsData = recordData.ingredients.map(ingredient => ({
        ...ingredient,
        diet_record_id: record.id,
      }));

      const { error: ingredientsError } = await supabase
        .from(TABLES.INGREDIENTS)
        .insert(ingredientsData);

      if (ingredientsError) throw ingredientsError;
    }

    return record;
  }

  // 获取用户的饮食记录
  static async getUserDietRecords(userId: string, limit = 50, offset = 0): Promise<DietRecord[]> {
    const { data, error } = await supabase
      .from(TABLES.DIET_RECORDS)
      .select(`
        *,
        ingredients (*)
      `)
      .eq('user_id', userId)
      .order('record_time', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;
    return data || [];
  }

  // 删除饮食记录
  static async deleteDietRecord(id: string, userId: string): Promise<void> {
    const { error } = await supabase
      .from(TABLES.DIET_RECORDS)
      .delete()
      .eq('id', id)
      .eq('user_id', userId);

    if (error) throw error;
  }
}

// 菜品库相关操作
export class DishService {
  // 添加菜品到库中
  static async addDishes(dishes: { name: string; category: string }[]): Promise<void> {
    const { error } = await supabase
      .from(TABLES.DISHES)
      .upsert(dishes, { onConflict: 'name,category' });

    if (error) throw error;
  }

  // 获取所有菜品
  static async getAllDishes(): Promise<Dish[]> {
    const { data, error } = await supabase
      .from(TABLES.DISHES)
      .select('*')
      .order('name');

    if (error) throw error;
    return data || [];
  }

  // 搜索菜品
  static async searchDishes(query: string): Promise<Dish[]> {
    const { data, error } = await supabase
      .from(TABLES.DISHES)
      .select('*')
      .ilike('name', `%${query}%`)
      .order('name')
      .limit(20);

    if (error) throw error;
    return data || [];
  }
}
