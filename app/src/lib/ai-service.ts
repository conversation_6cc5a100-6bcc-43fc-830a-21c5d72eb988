import axios from 'axios';
import { AIAnalysisResult, CalorieAnalysisResult } from '@/types';

export interface AIConfig {
  baseURL: string;
  apiKey: string;
  authCode?: string;
  model: string;
}

export class AIService {
  private config: AIConfig;

  constructor(config: AIConfig) {
    this.config = config;
  }

  // 构建API URL
  private buildApiUrl(endpoint: string): string {
    const baseUrl = this.config.baseURL.replace(/\/$/, '');

    // 如果是您的网关，使用授权码作为路径参数
    if (baseUrl.includes('gateway.988886.xyz')) {
      if (this.config.authCode && this.config.authCode.trim()) {
        return `${baseUrl}/${this.config.authCode}${endpoint}`;
      }
      // 如果没有授权码，尝试使用API密钥
      return `${baseUrl}/${this.config.apiKey}${endpoint}`;
    }

    // 如果有授权码，使用授权码
    if (this.config.authCode && this.config.authCode.trim()) {
      return `${baseUrl}/${this.config.authCode}${endpoint}`;
    }

    return `${baseUrl}${endpoint}`;
  }

  // 获取可用模型列表
  async getAvailableModels(): Promise<string[]> {
    const baseUrl = this.config.baseURL.replace(/\/$/, '');

    // 如果是您的网关，尝试多种路径格式
    if (baseUrl.includes('gateway.988886.xyz')) {
      const urlsToTry = [
        `${baseUrl}/v1/models`, // 格式1: 授权码通过header
        `${baseUrl}/${this.config.authCode || this.config.apiKey}/v1/models`, // 格式2: 授权码在路径中
        `${baseUrl}/${this.config.authCode || this.config.apiKey}/models`, // 格式3: 无v1前缀
        `${baseUrl}/api/v1/models`, // 格式4: 带api前缀
        `${baseUrl}/${this.config.authCode || this.config.apiKey}/api/v1/models`, // 格式5: 授权码+api前缀
        `${baseUrl}/openai/v1/models`, // 格式6: 带openai前缀
        `${baseUrl}/${this.config.authCode || this.config.apiKey}/openai/v1/models`, // 格式7: 授权码+openai前缀
      ];

      for (const url of urlsToTry) {
        try {
          console.log('尝试URL:', url);

          const response = await axios.get(url, {
            headers: {
              'Authorization': `Bearer ${this.config.apiKey}`,
              'Content-Type': 'application/json',
              ...(this.config.authCode && { 'X-Auth-Code': this.config.authCode }),
            },
          });

          console.log('API响应:', response.data);

          if (response.data && response.data.data) {
            return response.data.data.map((model: { id: string }) => model.id);
          }

          // 如果响应格式不同，尝试其他可能的格式
          if (response.data && Array.isArray(response.data)) {
            return response.data.map((model: { id: string }) => model.id);
          }

          return [];
        } catch (error) {
          console.log(`URL ${url} 失败:`, (error as any).response?.status);
          continue;
        }
      }

      console.error('所有URL都失败了');
      return [];
    }

    // 标准OpenAI格式
    try {
      const url = this.buildApiUrl('/v1/models');
      console.log('请求URL:', url);

      const response = await axios.get(url, {
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.data && response.data.data) {
        return response.data.data.map((model: { id: string }) => model.id);
      }
      return [];
    } catch (error) {
      console.error('获取模型列表失败:', error);
      return [];
    }
  }

  // 分析饮食输入（文字）
  async analyzeDietText(input: string): Promise<AIAnalysisResult> {
    const prompt = `
请分析以下饮食描述，提取出具体的食材、数量和菜品信息。请严格按照JSON格式返回，不要包含任何其他文字：

用户输入：${input}

请返回JSON格式：
{
  "ingredients": [
    {
      "name": "食材名称",
      "amount": 数量(数字),
      "unit": "单位(g/ml/个等)"
    }
  ],
  "dishes": [
    {
      "name": "菜品名称",
      "category": "dish|staple|drink|side"
    }
  ],
  "meal_type": "breakfast|lunch|dinner|snack",
  "estimated_time": "YYYY-MM-DD HH:mm:ss"
}

注意：
1. 根据当前北京时间判断餐次
2. 尽量准确估算食材重量
3. 菜品分类：dish(菜品)、staple(主食)、drink(饮料)、side(配菜)
4. 只返回JSON，不要其他解释
`;

    try {
      const response = await axios.post(
        this.buildApiUrl('/v1/chat/completions'),
        {
          model: this.config.model,
          messages: [
            {
              role: 'user',
              content: prompt,
            },
          ],
          temperature: 0.3,
          max_tokens: 1000,
        },
        {
          headers: {
            'Authorization': `Bearer ${this.config.apiKey}`,
            'Content-Type': 'application/json',
          },
        }
      );

      const content = response.data.choices[0].message.content;
      return JSON.parse(content);
    } catch (error) {
      console.error('AI分析失败:', error);
      throw new Error('AI分析失败，请稍后重试');
    }
  }

  // 分析饮食输入（图片）
  async analyzeDietImage(imageUrl: string, additionalText?: string): Promise<AIAnalysisResult> {
    const prompt = `
请分析这张食物图片，识别出具体的食材、数量和菜品信息。${additionalText ? `用户补充说明：${additionalText}` : ''}

请严格按照JSON格式返回，不要包含任何其他文字：

{
  "ingredients": [
    {
      "name": "食材名称",
      "amount": 数量(数字),
      "unit": "单位(g/ml/个等)"
    }
  ],
  "dishes": [
    {
      "name": "菜品名称",
      "category": "dish|staple|drink|side"
    }
  ],
  "meal_type": "breakfast|lunch|dinner|snack",
  "estimated_time": "YYYY-MM-DD HH:mm:ss"
}

注意：
1. 根据当前北京时间判断餐次
2. 根据图片中食物的视觉大小估算重量
3. 菜品分类：dish(菜品)、staple(主食)、drink(饮料)、side(配菜)
4. 只返回JSON，不要其他解释
`;

    try {
      const response = await axios.post(
        this.buildApiUrl('/v1/chat/completions'),
        {
          model: this.config.model,
          messages: [
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: prompt,
                },
                {
                  type: 'image_url',
                  image_url: {
                    url: imageUrl,
                  },
                },
              ],
            },
          ],
          temperature: 0.3,
          max_tokens: 1000,
        },
        {
          headers: {
            'Authorization': `Bearer ${this.config.apiKey}`,
            'Content-Type': 'application/json',
          },
        }
      );

      const content = response.data.choices[0].message.content;
      return JSON.parse(content);
    } catch (error) {
      console.error('AI图片分析失败:', error);
      throw new Error('AI图片分析失败，请稍后重试');
    }
  }

  // 分析热量和营养
  async analyzeCalories(ingredients: { name: string; amount: number; unit: string }[]): Promise<CalorieAnalysisResult> {
    const ingredientsList = ingredients
      .map(ing => `${ing.name} ${ing.amount}${ing.unit}`)
      .join('、');

    const prompt = `
请分析以下食材的热量和营养信息：

食材清单：${ingredientsList}

请严格按照JSON格式返回，不要包含任何其他文字：

{
  "total_calories": 总热量(数字),
  "analysis": "简短的营养分析和建议(50字以内)",
  "breakdown": [
    {
      "ingredient": "食材名称",
      "calories": 该食材热量(数字)
    }
  ]
}

注意：
1. 热量计算要准确，基于实际营养数据
2. 分析要简洁实用
3. 只返回JSON，不要其他解释
`;

    try {
      const response = await axios.post(
        this.buildApiUrl('/v1/chat/completions'),
        {
          model: this.config.model,
          messages: [
            {
              role: 'user',
              content: prompt,
            },
          ],
          temperature: 0.3,
          max_tokens: 800,
        },
        {
          headers: {
            'Authorization': `Bearer ${this.config.apiKey}`,
            'Content-Type': 'application/json',
          },
        }
      );

      const content = response.data.choices[0].message.content;
      return JSON.parse(content);
    } catch (error) {
      console.error('热量分析失败:', error);
      throw new Error('热量分析失败，请稍后重试');
    }
  }
}

// 创建AI服务实例的工厂函数
export function createAIService(config: AIConfig): AIService {
  return new AIService(config);
}

// 默认配置
export const getDefaultAIConfig = (): AIConfig => ({
  baseURL: process.env.DEFAULT_AI_BASE_URL || 'https://api.openai.com',
  apiKey: process.env.DEFAULT_AI_API_KEY || '',
  model: 'gpt-4o',
});
