(()=>{var a={};a.id=347,a.ids=[347],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.d(b,{AM:()=>i,DL:()=>h,Qx:()=>k,d2:()=>j});var e=c(56621),f=c(93139),g=a([f]);f=(g.then?(await g)():g)[0];class h{static async createUser(a){if(!e.jD)throw Error("数据库未配置，请设置正确的环境变量");let b=await f.default.hash(a.password,12),{data:c,error:d}=await e.E2.from(e.CG.USERS).insert({username:a.username,email:a.email,password_hash:b,role:a.role||"user",auth_code:a.auth_code}).select("id, username, email, role, auth_code, created_at, updated_at").single();if(d)throw d;return c}static async validateUser(a,b){let{data:c,error:d}=await e.E2.from(e.CG.USERS).select("*").eq("username",a).single();if(d||!c||!await f.default.compare(b,c.password_hash))return null;let{password_hash:g,...h}=c;return h}static async getUserById(a){let{data:b,error:c}=await e.ND.from(e.CG.USERS).select("id, username, email, role, auth_code, created_at, updated_at").eq("id",a).single();return c?null:b}static async getAllUsers(){let{data:a,error:b}=await e.E2.from(e.CG.USERS).select("id, username, email, role, auth_code, created_at, updated_at").order("created_at",{ascending:!1});if(b)throw b;return a||[]}static async updateUser(a,b){let{data:c,error:d}=await e.E2.from(e.CG.USERS).update(b).eq("id",a).select("id, username, email, role, auth_code, created_at, updated_at").single();if(d)throw d;return c}static async resetPassword(a,b){let c=await f.default.hash(b,12),{error:d}=await e.E2.from(e.CG.USERS).update({password_hash:c}).eq("id",a);if(d)throw d}static async deleteUser(a){let{error:b}=await e.E2.from(e.CG.USERS).delete().eq("id",a);if(b)throw b}}class i{static async getUserSettings(a){let{data:b,error:c}=await e.ND.from(e.CG.USER_SETTINGS).select("*").eq("user_id",a).single();return c?null:b}static async updateUserSettings(a,b){let{data:c,error:d}=await e.ND.from(e.CG.USER_SETTINGS).upsert({user_id:a,...b}).select().single();if(d)throw d;return c}}class j{static async createDietRecord(a){let{data:b,error:c}=await e.ND.from(e.CG.DIET_RECORDS).insert({user_id:a.user_id,original_input:a.original_input,input_type:a.input_type,image_url:a.image_url,meal_type:a.meal_type,record_time:a.record_time,total_calories:a.total_calories,ai_analysis:a.ai_analysis,dishes:a.dishes}).select().single();if(c)throw c;if(a.ingredients.length>0){let c=a.ingredients.map(a=>({...a,diet_record_id:b.id})),{error:d}=await e.ND.from(e.CG.INGREDIENTS).insert(c);if(d)throw d}return b}static async getUserDietRecords(a,b=50,c=0){let{data:d,error:f}=await e.ND.from(e.CG.DIET_RECORDS).select(`
        *,
        ingredients (*)
      `).eq("user_id",a).order("record_time",{ascending:!1}).range(c,c+b-1);if(f)throw f;return d||[]}static async deleteDietRecord(a,b){let{error:c}=await e.ND.from(e.CG.DIET_RECORDS).delete().eq("id",a).eq("user_id",b);if(c)throw c}}class k{static async addDishes(a){let{error:b}=await e.ND.from(e.CG.DISHES).upsert(a,{onConflict:"name,category"});if(b)throw b}static async getAllDishes(){let{data:a,error:b}=await e.ND.from(e.CG.DISHES).select("*").order("name");if(b)throw b;return a||[]}static async searchDishes(a){let{data:b,error:c}=await e.ND.from(e.CG.DISHES).select("*").ilike("name",`%${a}%`).order("name").limit(20);if(c)throw c;return b||[]}}d()}catch(a){d(a)}})},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},21820:a=>{"use strict";a.exports=require("os")},27910:a=>{"use strict";a.exports=require("stream")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30755:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{handler:()=>x,patchFetch:()=>w,routeModule:()=>y,serverHooks:()=>B,workAsyncStorage:()=>z,workUnitAsyncStorage:()=>A});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(63501),v=a([u]);u=(v.then?(await v)():v)[0];let y=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/diet/records/[id]/route",pathname:"/api/diet/records/[id]",filename:"route",bundlePath:"app/api/diet/records/[id]/route"},distDir:".next",projectDir:"",resolvedPagePath:"/root/test/l-weight/app/src/app/api/diet/records/[id]/route.ts",nextConfigOutput:"standalone",userland:u}),{workAsyncStorage:z,workUnitAsyncStorage:A,serverHooks:B}=y;function w(){return(0,g.patchFetch)({workAsyncStorage:z,workUnitAsyncStorage:A})}async function x(a,b,c){var d;let e="/api/diet/records/[id]/route";"/index"===e&&(e="/");let g=await y.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:z,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(z.dynamicRoutes[E]||z.routes[D]);if(F&&!x){let a=!!z.routes[D],b=z.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||y.isDev||x||(G=D,G="/index"===G?"/":G);let H=!0===y.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:z,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>y.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>y.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await y.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await y.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:z,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await y.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}d()}catch(a){d(a)}})},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},56621:(a,b,c)=>{"use strict";c.d(b,{CG:()=>j,E2:()=>i,ND:()=>h,jD:()=>g});var d=c(66437);let e="https://bkgnzxuasasrzvcuwbfh.supabase.co",f=process.env.SUPABASE_SERVICE_ROLE_KEY||"placeholder-service-role-key",g=process.env.SUPABASE_SERVICE_ROLE_KEY&&!"https://bkgnzxuasasrzvcuwbfh.supabase.co".includes("placeholder")&&!"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJrZ256eHVhc2Fzcnp2Y3V3YmZoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM5MzEwNTYsImV4cCI6MjA2OTUwNzA1Nn0.eFq5bPZzHH-kTRKKVmv9NACXycjpziPsRTDzkuy-dVU".includes("placeholder")&&!process.env.SUPABASE_SERVICE_ROLE_KEY.includes("placeholder"),h=(0,d.UU)(e,"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJrZ256eHVhc2Fzcnp2Y3V3YmZoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM5MzEwNTYsImV4cCI6MjA2OTUwNzA1Nn0.eFq5bPZzHH-kTRKKVmv9NACXycjpziPsRTDzkuy-dVU"),i=(0,d.UU)(e,f),j={USERS:"users",USER_SETTINGS:"user_settings",DIET_RECORDS:"diet_records",INGREDIENTS:"ingredients",DISHES:"dishes"}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63501:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{DELETE:()=>h});var e=c(32190),f=c(6710),g=a([f]);async function h(a,{params:b}){try{let c=a.headers.get("authorization");if(!c)return e.NextResponse.json({success:!1,error:"未授权访问"},{status:401});let d=JSON.parse(c.replace("Bearer ","")),g=await b;return await f.d2.deleteDietRecord(g.id,d.id),e.NextResponse.json({success:!0,message:"饮食记录删除成功"})}catch(a){return console.error("删除饮食记录错误:",a),e.NextResponse.json({success:!1,error:"删除饮食记录失败"},{status:500})}}f=(g.then?(await g)():g)[0],d()}catch(a){d(a)}})},74075:a=>{"use strict";a.exports=require("zlib")},78335:()=>{},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},93139:a=>{"use strict";a.exports=import("bcryptjs")},94735:a=>{"use strict";a.exports=require("events")},96487:()=>{}};var b=require("../../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[96],()=>b(b.s=30755));module.exports=c})();