(()=>{var a={};a.id=698,a.ids=[698],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1132:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>g});var d=c(37413),e=c(97932),f=c(65537);function g(){return(0,d.jsx)(e.AuthGuard,{requireAuth:!0,requireAdmin:!0,children:(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,d.jsx)("div",{className:"flex items-center",children:(0,d.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"管理员控制台"})}),(0,d.jsx)("div",{className:"flex items-center space-x-4",children:(0,d.jsx)("a",{href:"/dashboard",className:"text-sm text-gray-600 hover:text-gray-900",children:"返回主页"})})]})})}),(0,d.jsx)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,d.jsx)(f.AdminDashboard,{})})]})})}},2027:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3918:(a,b,c)=>{Promise.resolve().then(c.bind(c,64616))},4780:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(49384),e=c(82348);function f(...a){return(0,e.QP)((0,d.$)(a))}},10268:(a,b,c)=>{Promise.resolve().then(c.bind(c,65537)),Promise.resolve().then(c.bind(c,97932))},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},13337:(a,b,c)=>{"use strict";c.d(b,{n:()=>f});var d=c(26787),e=c(59350);let f=(0,d.v)()((0,e.Zr)((a,b)=>({user:null,isAuthenticated:!1,isLoading:!1,login:b=>{a({user:b,isAuthenticated:!0,isLoading:!1})},logout:()=>{a({user:null,isAuthenticated:!1,isLoading:!1})},setLoading:b=>{a({isLoading:b})},updateUser:c=>{let d=b().user;d&&a({user:{...d,...c}})}}),{name:"auth-storage",partialize:a=>({user:a.user,isAuthenticated:a.isAuthenticated})}))},17830:(a,b,c)=>{Promise.resolve().then(c.bind(c,48482))},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19996:(a,b,c)=>{Promise.resolve().then(c.bind(c,36458)),Promise.resolve().then(c.bind(c,20666))},20666:(a,b,c)=>{"use strict";c.d(b,{AuthGuard:()=>g});var d=c(60687);c(43210);var e=c(16189),f=c(13337);function g({children:a,requireAuth:b=!0,requireAdmin:c=!1,redirectTo:g}){let{isAuthenticated:h,user:i,isLoading:j}=(0,f.n)();return((0,e.useRouter)(),j)?(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"})}):b&&!h||c&&i?.role!=="admin"||!b&&h?null:(0,d.jsx)(d.Fragment,{children:a})}},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(a,b,c)=>{"use strict";c.d(b,{$:()=>i});var d=c(60687);c(43210);var e=c(8730),f=c(24224),g=c(4780);let h=(0,f.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i({className:a,variant:b,size:c,asChild:f=!1,...i}){let j=f?e.DX:"button";return(0,d.jsx)(j,{"data-slot":"button",className:(0,g.cn)(h({variant:b,size:c,className:a})),...i})}},33873:a=>{"use strict";a.exports=require("path")},36458:(a,b,c)=>{"use strict";c.d(b,{AdminDashboard:()=>G});var d=c(60687),e=c(43210),f=c(27605),g=c(63442),h=c(37566),i=c(52581),j=c(51060),k=c(13644),l=c(6996),m=c(29523),n=c(89667),o=c(80013),p=c(44493),q=c(63503),r=c(4780);function s({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,d.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm",a),...b})})}function t({className:a,...b}){return(0,d.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",a),...b})}function u({className:a,...b}){return(0,d.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",a),...b})}function v({className:a,...b}){return(0,d.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",a),...b})}function w({className:a,...b}){return(0,d.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...b})}function x({className:a,...b}){return(0,d.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...b})}var y=c(41862),z=c(78122),A=c(96474),B=c(19959),C=c(88233),D=c(13337);let E=h.Ik({username:h.Yj().min(3,"用户名至少3个字符").max(50,"用户名最多50个字符"),email:h.Yj().email("邮箱格式不正确"),password:h.Yj().min(6,"密码至少6个字符"),role:h.k5(["admin","user"]),auth_code:h.Yj().optional()}),F=h.Ik({newPassword:h.Yj().min(6,"密码至少6个字符")});function G(){let[a,b]=(0,e.useState)([]),[c,h]=(0,e.useState)(!0),[r,G]=(0,e.useState)(!1),[H,I]=(0,e.useState)(!1),[J,K]=(0,e.useState)(null),[L,M]=(0,e.useState)(!1),[N,O]=(0,e.useState)(!1),{user:P}=(0,D.n)(),{register:Q,handleSubmit:R,formState:{errors:S},reset:T}=(0,f.mN)({resolver:(0,g.u)(E),defaultValues:{role:"user"}}),{register:U,handleSubmit:V,formState:{errors:W},reset:X}=(0,f.mN)({resolver:(0,g.u)(F)}),Y=async()=>{if(P)try{h(!0);let a=await j.A.get("/api/admin/users",{headers:{Authorization:`Bearer ${JSON.stringify(P)}`}});a.data.success?b(a.data.data):i.oR.error("获取用户列表失败")}catch(a){console.error("获取用户列表错误:",a),i.oR.error(a.response?.data?.error||"获取用户列表失败")}finally{h(!1)}},Z=async c=>{if(P)try{M(!0);let d=await j.A.post("/api/admin/users",c,{headers:{Authorization:`Bearer ${JSON.stringify(P)}`,"Content-Type":"application/json"}});d.data.success?(i.oR.success("用户创建成功"),b([d.data.data,...a]),G(!1),T()):i.oR.error(d.data.error||"创建失败")}catch(a){console.error("创建用户错误:",a),i.oR.error(a.response?.data?.error||"创建失败")}finally{M(!1)}},$=async a=>{if(J&&P)try{O(!0);let b=await j.A.patch(`/api/admin/users/${J.id}`,{newPassword:a.newPassword},{headers:{Authorization:`Bearer ${JSON.stringify(P)}`,"Content-Type":"application/json"}});b.data.success?(i.oR.success("密码重置成功"),I(!1),K(null),X()):i.oR.error(b.data.error||"重置失败")}catch(a){console.error("重置密码错误:",a),i.oR.error(a.response?.data?.error||"重置失败")}finally{O(!1)}},_=async c=>{if(P&&confirm("确定要删除这个用户吗？此操作无法撤销。"))try{(await j.A.delete(`/api/admin/users/${c}`,{headers:{Authorization:`Bearer ${JSON.stringify(P)}`}})).data.success?(i.oR.success("用户删除成功"),b(a.filter(a=>a.id!==c))):i.oR.error("删除失败")}catch(a){console.error("删除用户错误:",a),i.oR.error(a.response?.data?.error||"删除失败")}};return c?(0,d.jsx)(p.Zp,{children:(0,d.jsxs)(p.Wu,{className:"flex items-center justify-center py-8",children:[(0,d.jsx)(y.A,{className:"w-6 h-6 animate-spin mr-2"}),"加载中..."]})}):(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-2xl font-bold",children:"用户管理"}),(0,d.jsx)("p",{className:"text-gray-600",children:"管理系统用户和权限"})]}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsxs)(m.$,{variant:"outline",onClick:Y,disabled:c,children:[(0,d.jsx)(z.A,{className:"w-4 h-4 mr-2"}),"刷新"]}),(0,d.jsxs)(m.$,{onClick:()=>G(!0),children:[(0,d.jsx)(A.A,{className:"w-4 h-4 mr-2"}),"创建用户"]})]})]}),(0,d.jsxs)(p.Zp,{children:[(0,d.jsxs)(p.aR,{children:[(0,d.jsx)(p.ZB,{children:"用户列表"}),(0,d.jsx)(p.BT,{children:"系统中的所有用户"})]}),(0,d.jsx)(p.Wu,{children:(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)(s,{children:[(0,d.jsx)(t,{children:(0,d.jsxs)(v,{children:[(0,d.jsx)(w,{children:"用户名"}),(0,d.jsx)(w,{className:"hidden sm:table-cell",children:"邮箱"}),(0,d.jsx)(w,{children:"角色"}),(0,d.jsx)(w,{className:"hidden md:table-cell",children:"授权码"}),(0,d.jsx)(w,{className:"hidden lg:table-cell",children:"创建时间"}),(0,d.jsx)(w,{children:"操作"})]})}),(0,d.jsx)(u,{children:a.map(a=>(0,d.jsxs)(v,{children:[(0,d.jsx)(x,{className:"font-medium",children:a.username}),(0,d.jsx)(x,{className:"hidden sm:table-cell",children:a.email}),(0,d.jsx)(x,{children:(0,d.jsx)("span",{className:`px-2 py-1 rounded-full text-xs ${"admin"===a.role?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"}`,children:"admin"===a.role?"管理员":"用户"})}),(0,d.jsx)(x,{className:"hidden md:table-cell",children:a.auth_code||"-"}),(0,d.jsx)(x,{className:"hidden lg:table-cell",children:(0,k.GP)(new Date(a.created_at),"yyyy-MM-dd HH:mm",{locale:l.g})}),(0,d.jsx)(x,{children:(0,d.jsxs)("div",{className:"flex gap-1",children:[(0,d.jsx)(m.$,{variant:"ghost",size:"sm",onClick:()=>{K(a),I(!0)},children:(0,d.jsx)(B.A,{className:"w-4 h-4"})}),a.id!==P?.id&&(0,d.jsx)(m.$,{variant:"ghost",size:"sm",onClick:()=>_(a.id),children:(0,d.jsx)(C.A,{className:"w-4 h-4 text-red-500"})})]})})]},a.id))})]})})})]}),(0,d.jsx)(q.lG,{open:r,onOpenChange:G,children:(0,d.jsxs)(q.Cf,{children:[(0,d.jsxs)(q.c7,{children:[(0,d.jsx)(q.L3,{children:"创建新用户"}),(0,d.jsx)(q.rr,{children:"填写用户信息创建新账号"})]}),(0,d.jsxs)("form",{onSubmit:R(Z),className:"space-y-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(o.J,{htmlFor:"username",children:"用户名"}),(0,d.jsx)(n.p,{id:"username",...Q("username"),disabled:L}),S.username&&(0,d.jsx)("p",{className:"text-sm text-red-500",children:S.username.message})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(o.J,{htmlFor:"email",children:"邮箱"}),(0,d.jsx)(n.p,{id:"email",type:"email",...Q("email"),disabled:L}),S.email&&(0,d.jsx)("p",{className:"text-sm text-red-500",children:S.email.message})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(o.J,{htmlFor:"password",children:"密码"}),(0,d.jsx)(n.p,{id:"password",type:"password",...Q("password"),disabled:L}),S.password&&(0,d.jsx)("p",{className:"text-sm text-red-500",children:S.password.message})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(o.J,{htmlFor:"auth_code",children:"授权码（可选）"}),(0,d.jsx)(n.p,{id:"auth_code",...Q("auth_code"),disabled:L})]}),(0,d.jsxs)(q.Es,{children:[(0,d.jsx)(m.$,{type:"button",variant:"outline",onClick:()=>G(!1),disabled:L,children:"取消"}),(0,d.jsx)(m.$,{type:"submit",disabled:L,children:L?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(y.A,{className:"w-4 h-4 mr-2 animate-spin"}),"创建中..."]}):"创建用户"})]})]})]})}),(0,d.jsx)(q.lG,{open:H,onOpenChange:I,children:(0,d.jsxs)(q.Cf,{children:[(0,d.jsxs)(q.c7,{children:[(0,d.jsx)(q.L3,{children:"重置密码"}),(0,d.jsxs)(q.rr,{children:["为用户 ",J?.username," 设置新密码"]})]}),(0,d.jsxs)("form",{onSubmit:V($),className:"space-y-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(o.J,{htmlFor:"newPassword",children:"新密码"}),(0,d.jsx)(n.p,{id:"newPassword",type:"password",...U("newPassword"),disabled:N}),W.newPassword&&(0,d.jsx)("p",{className:"text-sm text-red-500",children:W.newPassword.message})]}),(0,d.jsxs)(q.Es,{children:[(0,d.jsx)(m.$,{type:"button",variant:"outline",onClick:()=>I(!1),disabled:N,children:"取消"}),(0,d.jsx)(m.$,{type:"submit",disabled:N,children:N?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(y.A,{className:"w-4 h-4 mr-2 animate-spin"}),"重置中..."]}):"重置密码"})]})]})]})})]})}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},44493:(a,b,c)=>{"use strict";c.d(b,{BT:()=>i,Wu:()=>j,ZB:()=>h,Zp:()=>f,aR:()=>g});var d=c(60687);c(43210);var e=c(4780);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card",className:(0,e.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...b})}function g({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-header",className:(0,e.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...b})}function h({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-title",className:(0,e.cn)("leading-none font-semibold",a),...b})}function i({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-description",className:(0,e.cn)("text-muted-foreground text-sm",a),...b})}function j({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-content",className:(0,e.cn)("px-6",a),...b})}},48482:(a,b,c)=>{"use strict";c.d(b,{Toaster:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/root/test/l-weight/app/src/components/ui/sonner.tsx","Toaster")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},61135:()=>{},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(a,b,c)=>{"use strict";c.d(b,{Cf:()=>k,Es:()=>m,L3:()=>n,c7:()=>l,lG:()=>h,rr:()=>o});var d=c(60687);c(43210);var e=c(37908),f=c(11860),g=c(4780);function h({...a}){return(0,d.jsx)(e.bL,{"data-slot":"dialog",...a})}function i({...a}){return(0,d.jsx)(e.ZL,{"data-slot":"dialog-portal",...a})}function j({className:a,...b}){return(0,d.jsx)(e.hJ,{"data-slot":"dialog-overlay",className:(0,g.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...b})}function k({className:a,children:b,showCloseButton:c=!0,...h}){return(0,d.jsxs)(i,{"data-slot":"dialog-portal",children:[(0,d.jsx)(j,{}),(0,d.jsxs)(e.UC,{"data-slot":"dialog-content",className:(0,g.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...h,children:[b,c&&(0,d.jsxs)(e.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,d.jsx)(f.A,{}),(0,d.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function l({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"dialog-header",className:(0,g.cn)("flex flex-col gap-2 text-center sm:text-left",a),...b})}function m({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"dialog-footer",className:(0,g.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...b})}function n({className:a,...b}){return(0,d.jsx)(e.hE,{"data-slot":"dialog-title",className:(0,g.cn)("text-lg leading-none font-semibold",a),...b})}function o({className:a,...b}){return(0,d.jsx)(e.VY,{"data-slot":"dialog-description",className:(0,g.cn)("text-muted-foreground text-sm",a),...b})}},64616:(a,b,c)=>{"use strict";c.d(b,{Toaster:()=>g});var d=c(60687),e=c(10218),f=c(52581);let g=({...a})=>{let{theme:b="system"}=(0,e.D)();return(0,d.jsx)(f.l$,{theme:b,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...a})}},65075:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},65537:(a,b,c)=>{"use strict";c.d(b,{AdminDashboard:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call AdminDashboard() from the server but AdminDashboard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/root/test/l-weight/app/src/components/admin/AdminDashboard.tsx","AdminDashboard")},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},73611:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,1132)),"/root/test/l-weight/app/src/app/admin/page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"/root/test/l-weight/app/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/root/test/l-weight/app/src/app/admin/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/admin/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},74075:a=>{"use strict";a.exports=require("zlib")},79551:a=>{"use strict";a.exports=require("url")},80013:(a,b,c)=>{"use strict";c.d(b,{J:()=>g});var d=c(60687);c(43210);var e=c(78148),f=c(4780);function g({className:a,...b}){return(0,d.jsx)(e.b,{"data-slot":"label",className:(0,f.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...b})}},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},89667:(a,b,c)=>{"use strict";c.d(b,{p:()=>f});var d=c(60687);c(43210);var e=c(4780);function f({className:a,type:b,...c}){return(0,d.jsx)("input",{type:b,"data-slot":"input",className:(0,e.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...c})}},94431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>k,metadata:()=>j});var d=c(37413),e=c(2202),f=c.n(e),g=c(64988),h=c.n(g);c(61135);var i=c(48482);let j={title:"饮食热量分析系统",description:"智能分析您的饮食热量，助您健康生活"};function k({children:a}){return(0,d.jsx)("html",{lang:"zh-CN",children:(0,d.jsxs)("body",{className:`${f().variable} ${h().variable} antialiased`,children:[a,(0,d.jsx)(i.Toaster,{})]})})}},94735:a=>{"use strict";a.exports=require("events")},97932:(a,b,c)=>{"use strict";c.d(b,{AuthGuard:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call AuthGuard() from the server but AuthGuard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/root/test/l-weight/app/src/components/auth/AuthGuard.tsx","AuthGuard")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[96],()=>b(b.s=73611));module.exports=c})();