(()=>{var a={};a.id=105,a.ids=[105],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},2027:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3918:(a,b,c)=>{Promise.resolve().then(c.bind(c,64616))},4780:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(49384),e=c(82348);function f(...a){return(0,e.QP)((0,d.$)(a))}},10579:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,80559)),"/root/test/l-weight/app/src/app/dashboard/page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"/root/test/l-weight/app/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/root/test/l-weight/app/src/app/dashboard/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/dashboard/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},13337:(a,b,c)=>{"use strict";c.d(b,{n:()=>f});var d=c(26787),e=c(59350);let f=(0,d.v)()((0,e.Zr)((a,b)=>({user:null,isAuthenticated:!1,isLoading:!1,login:b=>{a({user:b,isAuthenticated:!0,isLoading:!1})},logout:()=>{a({user:null,isAuthenticated:!1,isLoading:!1})},setLoading:b=>{a({isLoading:b})},updateUser:c=>{let d=b().user;d&&a({user:{...d,...c}})}}),{name:"auth-storage",partialize:a=>({user:a.user,isAuthenticated:a.isAuthenticated})}))},17830:(a,b,c)=>{Promise.resolve().then(c.bind(c,48482))},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20666:(a,b,c)=>{"use strict";c.d(b,{AuthGuard:()=>g});var d=c(60687);c(43210);var e=c(16189),f=c(13337);function g({children:a,requireAuth:b=!0,requireAdmin:c=!1,redirectTo:g}){let{isAuthenticated:h,user:i,isLoading:j}=(0,f.n)();return((0,e.useRouter)(),j)?(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"})}):b&&!h||c&&i?.role!=="admin"||!b&&h?null:(0,d.jsx)(d.Fragment,{children:a})}},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},28587:(a,b,c)=>{Promise.resolve().then(c.bind(c,20666)),Promise.resolve().then(c.bind(c,76382))},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(a,b,c)=>{"use strict";c.d(b,{$:()=>i});var d=c(60687);c(43210);var e=c(8730),f=c(24224),g=c(4780);let h=(0,f.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i({className:a,variant:b,size:c,asChild:f=!1,...i}){let j=f?e.DX:"button";return(0,d.jsx)(j,{"data-slot":"button",className:(0,g.cn)(h({variant:b,size:c,className:a})),...i})}},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},44493:(a,b,c)=>{"use strict";c.d(b,{BT:()=>i,Wu:()=>j,ZB:()=>h,Zp:()=>f,aR:()=>g});var d=c(60687);c(43210);var e=c(4780);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card",className:(0,e.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...b})}function g({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-header",className:(0,e.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...b})}function h({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-title",className:(0,e.cn)("leading-none font-semibold",a),...b})}function i({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-description",className:(0,e.cn)("text-muted-foreground text-sm",a),...b})}function j({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-content",className:(0,e.cn)("px-6",a),...b})}},47178:(a,b,c)=>{"use strict";c.d(b,{DashboardContent:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call DashboardContent() from the server but DashboardContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/root/test/l-weight/app/src/components/dashboard/DashboardContent.tsx","DashboardContent")},48482:(a,b,c)=>{"use strict";c.d(b,{Toaster:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/root/test/l-weight/app/src/components/ui/sonner.tsx","Toaster")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},61135:()=>{},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(a,b,c)=>{"use strict";c.d(b,{Cf:()=>k,Es:()=>m,L3:()=>n,c7:()=>l,lG:()=>h,rr:()=>o});var d=c(60687);c(43210);var e=c(37908),f=c(11860),g=c(4780);function h({...a}){return(0,d.jsx)(e.bL,{"data-slot":"dialog",...a})}function i({...a}){return(0,d.jsx)(e.ZL,{"data-slot":"dialog-portal",...a})}function j({className:a,...b}){return(0,d.jsx)(e.hJ,{"data-slot":"dialog-overlay",className:(0,g.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...b})}function k({className:a,children:b,showCloseButton:c=!0,...h}){return(0,d.jsxs)(i,{"data-slot":"dialog-portal",children:[(0,d.jsx)(j,{}),(0,d.jsxs)(e.UC,{"data-slot":"dialog-content",className:(0,g.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...h,children:[b,c&&(0,d.jsxs)(e.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,d.jsx)(f.A,{}),(0,d.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function l({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"dialog-header",className:(0,g.cn)("flex flex-col gap-2 text-center sm:text-left",a),...b})}function m({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"dialog-footer",className:(0,g.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...b})}function n({className:a,...b}){return(0,d.jsx)(e.hE,{"data-slot":"dialog-title",className:(0,g.cn)("text-lg leading-none font-semibold",a),...b})}function o({className:a,...b}){return(0,d.jsx)(e.VY,{"data-slot":"dialog-description",className:(0,g.cn)("text-muted-foreground text-sm",a),...b})}},64616:(a,b,c)=>{"use strict";c.d(b,{Toaster:()=>g});var d=c(60687),e=c(10218),f=c(52581);let g=({...a})=>{let{theme:b="system"}=(0,e.D)();return(0,d.jsx)(f.l$,{theme:b,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...a})}},65075:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},68339:(a,b,c)=>{Promise.resolve().then(c.bind(c,97932)),Promise.resolve().then(c.bind(c,47178))},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},74075:a=>{"use strict";a.exports=require("zlib")},76382:(a,b,c)=>{"use strict";c.d(b,{DashboardContent:()=>_});var d=c(60687),e=c(43210),f=c(16189),g=c(29523),h=c(44493),i=c(40083),j=c(96474),k=c(39916),l=c(84027),m=c(13337);let n=(0,c(26787).v)(a=>({currentInput:"",currentInputType:"text",currentImageUrl:void 0,analysisResult:null,calorieResult:null,dietRecords:[],isAnalyzing:!1,isSubmitting:!1,setCurrentInput:(b,c,d)=>a({currentInput:b,currentInputType:c,currentImageUrl:d}),setAnalysisResult:b=>a({analysisResult:b}),setCalorieResult:b=>a({calorieResult:b}),setDietRecords:b=>a({dietRecords:b}),addDietRecord:b=>a(a=>({dietRecords:[b,...a.dietRecords]})),removeDietRecord:b=>a(a=>({dietRecords:a.dietRecords.filter(a=>a.id!==b)})),setIsAnalyzing:b=>a({isAnalyzing:b}),setIsSubmitting:b=>a({isSubmitting:b}),reset:()=>a({currentInput:"",currentInputType:"text",currentImageUrl:void 0,analysisResult:null,calorieResult:null,isAnalyzing:!1,isSubmitting:!1})}));var o=c(27605),p=c(63442),q=c(37566),r=c(52581),s=c(51060),t=c(30474),u=c(89667),v=c(80013),w=c(4780);function x({className:a,...b}){return(0,d.jsx)("textarea",{"data-slot":"textarea",className:(0,w.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...b})}var y=c(51361),z=c(16023),A=c(41862);let B=q.Ik({input:q.Yj().min(1,"请输入饮食内容或上传图片")});function C(){let[a,b]=(0,e.useState)("text"),[c,f]=(0,e.useState)(null),[i,j]=(0,e.useState)(null),k=(0,e.useRef)(null),{user:l}=(0,m.n)(),{setCurrentInput:q,setIsAnalyzing:w,isAnalyzing:C}=n(),{register:D,handleSubmit:E,formState:{errors:F},reset:G}=(0,o.mN)({resolver:(0,p.u)(B)}),H=async d=>{if(!l)return void r.oR.error("请先登录");w(!0);try{let e;if("image"===a&&c){let a=new FormData;a.append("file",c);let b=await s.A.post("/api/upload/image",a,{headers:{Authorization:`Bearer ${JSON.stringify(l)}`,"Content-Type":"multipart/form-data"}});if(b.data.success)e=b.data.data.thumbnailUrl;else throw Error("图片上传失败")}let g=await s.A.post("/api/ai/analyze-diet",{input:d.input,inputType:a,imageUrl:e},{headers:{Authorization:`Bearer ${JSON.stringify(l)}`,"Content-Type":"application/json"}});if(g.data.success)q(d.input,a,e),n.getState().setAnalysisResult(g.data.data),r.oR.success("分析完成"),G(),f(null),j(null),b("text");else throw Error(g.data.error||"分析失败")}catch(a){console.error("饮食分析错误:",a),r.oR.error(a.response?.data?.error||"分析失败，请稍后重试")}finally{w(!1)}};return(0,d.jsxs)(h.Zp,{className:"w-full max-w-2xl mx-auto",children:[(0,d.jsxs)(h.aR,{children:[(0,d.jsx)(h.ZB,{children:"饮食记录"}),(0,d.jsx)(h.BT,{children:"输入您的饮食内容或上传食物图片，AI将为您分析食材和热量"})]}),(0,d.jsx)(h.Wu,{children:(0,d.jsxs)("form",{onSubmit:E(H),className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex gap-4",children:[(0,d.jsx)(g.$,{type:"button",variant:"text"===a?"default":"outline",onClick:()=>b("text"),className:"flex-1",children:"文字输入"}),(0,d.jsxs)(g.$,{type:"button",variant:"image"===a?"default":"outline",onClick:()=>b("image"),className:"flex-1",children:[(0,d.jsx)(y.A,{className:"w-4 h-4 mr-2"}),"图片上传"]})]}),"text"===a&&(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(v.J,{htmlFor:"input",children:"饮食描述"}),(0,d.jsx)(x,{id:"input",placeholder:"请描述您吃了什么，例如：一碗米饭，红烧肉，青菜...",...D("input"),disabled:C,rows:4}),F.input&&(0,d.jsx)("p",{className:"text-sm text-red-500",children:F.input.message})]}),"image"===a&&(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(v.J,{htmlFor:"input",children:"补充说明（可选）"}),(0,d.jsx)(u.p,{id:"input",placeholder:"可以补充说明食物的具体信息...",...D("input"),disabled:C})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(v.J,{children:"上传食物图片"}),(0,d.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center",children:[i?(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)(t.default,{src:i,alt:"预览",width:400,height:300,className:"max-w-full max-h-64 mx-auto rounded-lg object-contain"}),(0,d.jsx)(g.$,{type:"button",variant:"outline",onClick:()=>{f(null),j(null),k.current&&(k.current.value="")},children:"重新选择"})]}):(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)(z.A,{className:"w-12 h-12 mx-auto text-gray-400"}),(0,d.jsxs)("div",{children:[(0,d.jsx)(g.$,{type:"button",variant:"outline",onClick:()=>k.current?.click(),children:"选择图片"}),(0,d.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"支持 JPEG、PNG、WebP 格式，最大 10MB"})]})]}),(0,d.jsx)("input",{ref:k,type:"file",accept:"image/jpeg,image/jpg,image/png,image/webp",onChange:a=>{let b=a.target.files?.[0];if(b){if(!["image/jpeg","image/jpg","image/png","image/webp"].includes(b.type))return void r.oR.error("只支持 JPEG、PNG、WebP 格式的图片");if(b.size>0xa00000)return void r.oR.error("图片大小不能超过 10MB");f(b);let a=new FileReader;a.onload=a=>{j(a.target?.result)},a.readAsDataURL(b)}},className:"hidden"})]})]})]}),(0,d.jsx)(g.$,{type:"submit",className:"w-full",disabled:C||"image"===a&&!c,children:C?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(A.A,{className:"w-4 h-4 mr-2 animate-spin"}),"分析中..."]}):"开始分析"})]})})]})}var D=c(55491),E=c(78272),F=c(13964),G=c(3589);function H({...a}){return(0,d.jsx)(D.bL,{"data-slot":"select",...a})}function I({...a}){return(0,d.jsx)(D.WT,{"data-slot":"select-value",...a})}function J({className:a,size:b="default",children:c,...e}){return(0,d.jsxs)(D.l9,{"data-slot":"select-trigger","data-size":b,className:(0,w.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...e,children:[c,(0,d.jsx)(D.In,{asChild:!0,children:(0,d.jsx)(E.A,{className:"size-4 opacity-50"})})]})}function K({className:a,children:b,position:c="popper",...e}){return(0,d.jsx)(D.ZL,{children:(0,d.jsxs)(D.UC,{"data-slot":"select-content",className:(0,w.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===c&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:c,...e,children:[(0,d.jsx)(M,{}),(0,d.jsx)(D.LM,{className:(0,w.cn)("p-1","popper"===c&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:b}),(0,d.jsx)(N,{})]})})}function L({className:a,children:b,...c}){return(0,d.jsxs)(D.q7,{"data-slot":"select-item",className:(0,w.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...c,children:[(0,d.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,d.jsx)(D.VF,{children:(0,d.jsx)(F.A,{className:"size-4"})})}),(0,d.jsx)(D.p4,{children:b})]})}function M({className:a,...b}){return(0,d.jsx)(D.PP,{"data-slot":"select-scroll-up-button",className:(0,w.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(G.A,{className:"size-4"})})}function N({className:a,...b}){return(0,d.jsx)(D.wn,{"data-slot":"select-scroll-down-button",className:(0,w.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(E.A,{className:"size-4"})})}var O=c(88233);let P=q.Ik({ingredients:q.YO(q.Ik({name:q.Yj().min(1,"食材名称不能为空"),amount:q.ai().min(.1,"数量必须大于0"),unit:q.Yj().min(1,"单位不能为空")})),dishes:q.YO(q.Ik({name:q.Yj().min(1,"菜品名称不能为空"),category:q.k5(["dish","staple","drink","side"])})),meal_type:q.k5(["breakfast","lunch","dinner","snack"]),record_time:q.Yj()});function Q(){let{user:a}=(0,m.n)(),{analysisResult:b,currentInput:c,currentInputType:e,currentImageUrl:f,setIsSubmitting:i,isSubmitting:k,reset:l}=n(),{register:q,control:t,handleSubmit:w,formState:{errors:x},setValue:y,watch:z}=(0,o.mN)({resolver:(0,p.u)(P)}),{fields:B,append:C,remove:D}=(0,o.jz)({control:t,name:"ingredients"}),{fields:E,append:F,remove:G}=(0,o.jz)({control:t,name:"dishes"}),M=async b=>{if(!a)return void r.oR.error("请先登录");i(!0);try{let d=await s.A.post("/api/ai/analyze-calories",{ingredients:b.ingredients},{headers:{Authorization:`Bearer ${JSON.stringify(a)}`,"Content-Type":"application/json"}});if(!d.data.success)throw Error("热量分析失败");let g=d.data.data,h=await s.A.post("/api/diet/records",{original_input:c,input_type:e,image_url:f,meal_type:b.meal_type,record_time:b.record_time,total_calories:g.total_calories,ai_analysis:g.analysis,dishes:b.dishes,ingredients:b.ingredients.map((a,b)=>({...a,calories_per_100g:g.breakdown[b]?.calories||0}))},{headers:{Authorization:`Bearer ${JSON.stringify(a)}`,"Content-Type":"application/json"}});if(h.data.success)r.oR.success("饮食记录保存成功"),l();else throw Error(h.data.error||"保存失败")}catch(a){console.error("保存饮食记录错误:",a),r.oR.error(a.response?.data?.error||"保存失败，请稍后重试")}finally{i(!1)}};return b?(0,d.jsxs)(h.Zp,{className:"w-full max-w-4xl mx-auto",children:[(0,d.jsxs)(h.aR,{children:[(0,d.jsx)(h.ZB,{children:"确认分析结果"}),(0,d.jsx)(h.BT,{children:"请检查并修改AI分析的结果，确保信息准确"})]}),(0,d.jsx)(h.Wu,{children:(0,d.jsxs)("form",{onSubmit:w(M),className:"space-y-6",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(v.J,{children:"餐次"}),(0,d.jsxs)(H,{value:z("meal_type"),onValueChange:a=>y("meal_type",a),children:[(0,d.jsx)(J,{children:(0,d.jsx)(I,{placeholder:"选择餐次"})}),(0,d.jsxs)(K,{children:[(0,d.jsx)(L,{value:"breakfast",children:"早餐"}),(0,d.jsx)(L,{value:"lunch",children:"午餐"}),(0,d.jsx)(L,{value:"dinner",children:"晚餐"}),(0,d.jsx)(L,{value:"snack",children:"加餐"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(v.J,{htmlFor:"record_time",children:"记录时间"}),(0,d.jsx)(u.p,{id:"record_time",type:"datetime-local",...q("record_time"),disabled:k})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)(v.J,{className:"text-lg font-semibold",children:"食材清单"}),(0,d.jsxs)(g.$,{type:"button",variant:"outline",size:"sm",onClick:()=>C({name:"",amount:0,unit:"g"}),children:[(0,d.jsx)(j.A,{className:"w-4 h-4 mr-2"}),"添加食材"]})]}),B.map((a,b)=>(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 p-4 border rounded-lg",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(v.J,{children:"食材名称"}),(0,d.jsx)(u.p,{...q(`ingredients.${b}.name`),placeholder:"食材名称",disabled:k}),x.ingredients?.[b]?.name&&(0,d.jsx)("p",{className:"text-sm text-red-500",children:x.ingredients[b]?.name?.message})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(v.J,{children:"数量"}),(0,d.jsx)(u.p,{type:"number",step:"0.1",...q(`ingredients.${b}.amount`,{valueAsNumber:!0}),placeholder:"数量",disabled:k}),x.ingredients?.[b]?.amount&&(0,d.jsx)("p",{className:"text-sm text-red-500",children:x.ingredients[b]?.amount?.message})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(v.J,{children:"单位"}),(0,d.jsx)(u.p,{...q(`ingredients.${b}.unit`),placeholder:"单位",disabled:k}),x.ingredients?.[b]?.unit&&(0,d.jsx)("p",{className:"text-sm text-red-500",children:x.ingredients[b]?.unit?.message})]}),(0,d.jsx)("div",{className:"flex items-end",children:(0,d.jsx)(g.$,{type:"button",variant:"outline",size:"sm",onClick:()=>D(b),disabled:k,children:(0,d.jsx)(O.A,{className:"w-4 h-4"})})})]},a.id))]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)(v.J,{className:"text-lg font-semibold",children:"菜品清单"}),(0,d.jsxs)(g.$,{type:"button",variant:"outline",size:"sm",onClick:()=>F({name:"",category:"dish"}),children:[(0,d.jsx)(j.A,{className:"w-4 h-4 mr-2"}),"添加菜品"]})]}),E.map((a,b)=>(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border rounded-lg",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(v.J,{children:"菜品名称"}),(0,d.jsx)(u.p,{...q(`dishes.${b}.name`),placeholder:"菜品名称",disabled:k}),x.dishes?.[b]?.name&&(0,d.jsx)("p",{className:"text-sm text-red-500",children:x.dishes[b]?.name?.message})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(v.J,{children:"分类"}),(0,d.jsxs)(H,{value:z(`dishes.${b}.category`),onValueChange:a=>y(`dishes.${b}.category`,a),children:[(0,d.jsx)(J,{children:(0,d.jsx)(I,{placeholder:"选择分类"})}),(0,d.jsxs)(K,{children:[(0,d.jsx)(L,{value:"dish",children:"菜品"}),(0,d.jsx)(L,{value:"staple",children:"主食"}),(0,d.jsx)(L,{value:"drink",children:"饮料"}),(0,d.jsx)(L,{value:"side",children:"配菜"})]})]})]}),(0,d.jsx)("div",{className:"flex items-end",children:(0,d.jsx)(g.$,{type:"button",variant:"outline",size:"sm",onClick:()=>G(b),disabled:k,children:(0,d.jsx)(O.A,{className:"w-4 h-4"})})})]},a.id))]}),(0,d.jsxs)("div",{className:"flex gap-4",children:[(0,d.jsx)(g.$,{type:"button",variant:"outline",onClick:l,disabled:k,className:"flex-1",children:"取消"}),(0,d.jsx)(g.$,{type:"submit",disabled:k,className:"flex-1",children:k?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(A.A,{className:"w-4 h-4 mr-2 animate-spin"}),"保存中..."]}):"确认保存"})]})]})})]}):null}var R=c(13644),S=c(6996),T=c(63503),U=c(78122),V=c(94577),W=c(48730);function X(){let[a,b]=(0,e.useState)([]),[c,f]=(0,e.useState)(!0),[i,j]=(0,e.useState)(!1),[k,l]=(0,e.useState)(null),[n,o]=(0,e.useState)(!1),{user:p}=(0,m.n)(),q=async()=>{if(p)try{f(!0);let a=await s.A.get("/api/diet/records",{headers:{Authorization:`Bearer ${JSON.stringify(p)}`}});a.data.success?b(a.data.data):r.oR.error("获取历史记录失败")}catch(a){console.error("获取历史记录错误:",a),r.oR.error(a.response?.data?.error||"获取历史记录失败")}finally{f(!1)}},t=async()=>{if(k&&p)try{o(!0),(await s.A.delete(`/api/diet/records/${k.id}`,{headers:{Authorization:`Bearer ${JSON.stringify(p)}`}})).data.success?(r.oR.success("记录删除成功"),b(a.filter(a=>a.id!==k.id)),j(!1),l(null)):r.oR.error("删除失败")}catch(a){console.error("删除记录错误:",a),r.oR.error(a.response?.data?.error||"删除失败")}finally{o(!1)}};return c?(0,d.jsx)(h.Zp,{children:(0,d.jsxs)(h.Wu,{className:"flex items-center justify-center py-8",children:[(0,d.jsx)(A.A,{className:"w-6 h-6 animate-spin mr-2"}),"加载中..."]})}):(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-2xl font-bold",children:"饮食历史"}),(0,d.jsx)("p",{className:"text-gray-600",children:"查看您的饮食记录历史"})]}),(0,d.jsxs)(g.$,{variant:"outline",onClick:q,disabled:c,children:[(0,d.jsx)(U.A,{className:"w-4 h-4 mr-2"}),"刷新"]})]}),0===a.length?(0,d.jsx)(h.Zp,{children:(0,d.jsxs)(h.Wu,{className:"text-center py-8",children:[(0,d.jsx)(V.A,{className:"w-12 h-12 mx-auto text-gray-400 mb-4"}),(0,d.jsx)("p",{className:"text-gray-500",children:"还没有饮食记录"}),(0,d.jsx)("p",{className:"text-sm text-gray-400 mt-2",children:"开始记录您的第一餐吧！"})]})}):(0,d.jsx)("div",{className:"space-y-4",children:a.map(a=>{var b;return(0,d.jsxs)(h.Zp,{className:"hover:shadow-md transition-shadow",children:[(0,d.jsx)(h.aR,{className:"pb-3",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(W.A,{className:"w-4 h-4 text-gray-500"}),(0,d.jsx)("span",{className:"text-sm text-gray-600",children:(0,R.GP)(new Date(a.record_time),"yyyy年MM月dd日 HH:mm",{locale:S.g})}),(0,d.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full",children:{breakfast:"早餐",lunch:"午餐",dinner:"晚餐",snack:"加餐"}[b=a.meal_type]||b})]}),(0,d.jsx)(g.$,{variant:"ghost",size:"sm",onClick:()=>{l(a),j(!0)},children:(0,d.jsx)(O.A,{className:"w-4 h-4 text-red-500"})})]})}),(0,d.jsxs)(h.Wu,{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium text-sm text-gray-700 mb-2",children:"原始输入"}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-start space-y-2 sm:space-y-0 sm:space-x-3",children:["image"===a.input_type&&a.image_url&&(0,d.jsx)("img",{src:a.image_url,alt:"食物图片",className:"w-16 h-16 sm:w-20 sm:h-20 rounded-lg object-cover mx-auto sm:mx-0"}),(0,d.jsx)("p",{className:"text-sm text-gray-600 flex-1 text-center sm:text-left",children:a.original_input})]})]}),a.ingredients&&a.ingredients.length>0&&(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium text-sm text-gray-700 mb-2",children:"食材清单"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:a.ingredients.map((a,b)=>(0,d.jsxs)("span",{className:"px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full",children:[a.name," ",a.amount,a.unit]},b))})]}),a.dishes&&a.dishes.length>0&&(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium text-sm text-gray-700 mb-2",children:"菜品清单"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:a.dishes.map((a,b)=>(0,d.jsx)("span",{className:"px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full",children:a},b))})]}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between pt-2 border-t space-y-2 sm:space-y-0",children:[(0,d.jsx)("div",{className:"flex items-center justify-center sm:justify-start space-x-4",children:(0,d.jsxs)("span",{className:"text-lg font-semibold text-red-600",children:[a.total_calories," 卡路里"]})}),a.ai_analysis&&(0,d.jsx)("p",{className:"text-sm text-gray-600 max-w-md text-center sm:text-right",children:a.ai_analysis})]})]})]},a.id)})}),(0,d.jsx)(T.lG,{open:i,onOpenChange:j,children:(0,d.jsxs)(T.Cf,{children:[(0,d.jsxs)(T.c7,{children:[(0,d.jsx)(T.L3,{children:"确认删除"}),(0,d.jsx)(T.rr,{children:"您确定要删除这条饮食记录吗？此操作无法撤销。"})]}),(0,d.jsxs)(T.Es,{children:[(0,d.jsx)(g.$,{variant:"outline",onClick:()=>j(!1),disabled:n,children:"取消"}),(0,d.jsx)(g.$,{variant:"destructive",onClick:t,disabled:n,children:n?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(A.A,{className:"w-4 h-4 mr-2 animate-spin"}),"删除中..."]}):"确认删除"})]})]})})]})}var Y=c(45989);let Z=q.Ik({ai_model:q.Yj().min(1,"AI模型不能为空"),api_base_url:q.Yj().url("API地址格式不正确"),api_key:q.Yj().min(1,"API密钥不能为空")});function $(){let[a,b]=(0,e.useState)(!0),[c,f]=(0,e.useState)(!1),[i,j]=(0,e.useState)(!1),[k,l]=(0,e.useState)([]),[n,q]=(0,e.useState)(!1),{user:t}=(0,m.n)(),{register:w,handleSubmit:x,formState:{errors:y},setValue:z,watch:B,reset:C}=(0,o.mN)({resolver:(0,p.u)(Z),defaultValues:{ai_model:"gpt-4o",api_base_url:"https://api.openai.com",api_key:""}}),D=async()=>{if(t)try{q(!0);let a=await s.A.get("/api/ai/models",{headers:{Authorization:`Bearer ${JSON.stringify(t)}`}});a.data.success?l(a.data.data):r.oR.error("获取模型列表失败")}catch(a){console.error("获取模型列表错误:",a),r.oR.error("获取模型列表失败，请检查API配置")}finally{q(!1)}},E=async()=>{if(!t)return;let a=B();try{j(!0),await s.A.put("/api/user/settings",a,{headers:{Authorization:`Bearer ${JSON.stringify(t)}`,"Content-Type":"application/json"}});let b=await s.A.get("/api/ai/models",{headers:{Authorization:`Bearer ${JSON.stringify(t)}`}});b.data.success?(r.oR.success("连接测试成功"),l(b.data.data)):r.oR.error("连接测试失败")}catch(a){console.error("测试连接错误:",a),r.oR.error(a.response?.data?.error||"连接测试失败")}finally{j(!1)}},F=async a=>{if(t)try{f(!0);let b=await s.A.put("/api/user/settings",a,{headers:{Authorization:`Bearer ${JSON.stringify(t)}`,"Content-Type":"application/json"}});b.data.success?r.oR.success("设置保存成功"):r.oR.error(b.data.error||"保存失败")}catch(a){console.error("保存设置错误:",a),r.oR.error(a.response?.data?.error||"保存失败")}finally{f(!1)}};return a?(0,d.jsx)(h.Zp,{children:(0,d.jsxs)(h.Wu,{className:"flex items-center justify-center py-8",children:[(0,d.jsx)(A.A,{className:"w-6 h-6 animate-spin mr-2"}),"加载中..."]})}):(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-2xl font-bold",children:"设置"}),(0,d.jsx)("p",{className:"text-gray-600",children:"配置您的AI模型和API设置"})]}),(0,d.jsxs)(h.Zp,{children:[(0,d.jsxs)(h.aR,{children:[(0,d.jsx)(h.ZB,{children:"AI模型配置"}),(0,d.jsx)(h.BT,{children:"配置用于饮食分析的AI模型和API设置"})]}),(0,d.jsx)(h.Wu,{children:(0,d.jsxs)("form",{onSubmit:x(F),className:"space-y-6",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(v.J,{htmlFor:"api_base_url",children:"API基础URL"}),(0,d.jsx)(u.p,{id:"api_base_url",type:"url",placeholder:"https://api.openai.com",...w("api_base_url"),disabled:c}),y.api_base_url&&(0,d.jsx)("p",{className:"text-sm text-red-500",children:y.api_base_url.message})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(v.J,{htmlFor:"api_key",children:"API密钥"}),(0,d.jsx)(u.p,{id:"api_key",type:"password",placeholder:"输入您的API密钥",...w("api_key"),disabled:c}),y.api_key&&(0,d.jsx)("p",{className:"text-sm text-red-500",children:y.api_key.message})]}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)(g.$,{type:"button",variant:"outline",onClick:E,disabled:i||c,children:i?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(A.A,{className:"w-4 h-4 mr-2 animate-spin"}),"测试中..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(Y.A,{className:"w-4 h-4 mr-2"}),"测试连接"]})}),(0,d.jsx)(g.$,{type:"button",variant:"outline",onClick:D,disabled:n||c,children:n?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(A.A,{className:"w-4 h-4 mr-2 animate-spin"}),"获取中..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(U.A,{className:"w-4 h-4 mr-2"}),"获取模型列表"]})})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(v.J,{children:"AI模型"}),(0,d.jsxs)(H,{value:B("ai_model"),onValueChange:a=>z("ai_model",a),children:[(0,d.jsx)(J,{children:(0,d.jsx)(I,{placeholder:"选择AI模型"})}),(0,d.jsx)(K,{children:k.length>0?k.map(a=>(0,d.jsx)(L,{value:a,children:a},a)):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(L,{value:"gpt-4o",children:"gpt-4o"}),(0,d.jsx)(L,{value:"gpt-4o-mini",children:"gpt-4o-mini"}),(0,d.jsx)(L,{value:"gpt-4-turbo",children:"gpt-4-turbo"}),(0,d.jsx)(L,{value:"gpt-3.5-turbo",children:"gpt-3.5-turbo"})]})})]}),y.ai_model&&(0,d.jsx)("p",{className:"text-sm text-red-500",children:y.ai_model.message})]}),(0,d.jsx)(g.$,{type:"submit",disabled:c,className:"w-full",children:c?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(A.A,{className:"w-4 h-4 mr-2 animate-spin"}),"保存中..."]}):"保存设置"})]})})]})]})}function _(){let[a,b]=(0,e.useState)("input"),c=(0,f.useRouter)(),{user:o,logout:p}=(0,m.n)(),{analysisResult:q}=n();return(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,d.jsx)("div",{className:"flex items-center",children:(0,d.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"饮食热量分析系统"})}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)("span",{className:"text-sm text-gray-600",children:["欢迎，",o?.username]}),(0,d.jsxs)(g.$,{variant:"outline",size:"sm",onClick:()=>{p(),c.push("/login")},children:[(0,d.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"退出登录"]})]})]})})}),(0,d.jsx)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,d.jsxs)("aside",{className:"w-full lg:w-64 space-y-2",children:[(0,d.jsx)(h.Zp,{children:(0,d.jsx)(h.Wu,{className:"p-4",children:(0,d.jsxs)("nav",{className:"space-y-2",children:[(0,d.jsxs)(g.$,{variant:"input"===a?"default":"ghost",className:"w-full justify-start",onClick:()=>b("input"),children:[(0,d.jsx)(j.A,{className:"w-4 h-4 mr-2"}),"饮食记录"]}),(0,d.jsxs)(g.$,{variant:"history"===a?"default":"ghost",className:"w-full justify-start",onClick:()=>b("history"),children:[(0,d.jsx)(k.A,{className:"w-4 h-4 mr-2"}),"历史记录"]}),(0,d.jsxs)(g.$,{variant:"settings"===a?"default":"ghost",className:"w-full justify-start",onClick:()=>b("settings"),children:[(0,d.jsx)(l.A,{className:"w-4 h-4 mr-2"}),"设置"]})]})})}),o?.role==="admin"&&(0,d.jsxs)(h.Zp,{children:[(0,d.jsx)(h.aR,{className:"pb-3",children:(0,d.jsx)(h.ZB,{className:"text-sm",children:"管理员功能"})}),(0,d.jsx)(h.Wu,{className:"pt-0",children:(0,d.jsx)(g.$,{variant:"outline",size:"sm",className:"w-full",onClick:()=>c.push("/admin"),children:"用户管理"})})]})]}),(0,d.jsxs)("div",{className:"flex-1",children:["input"===a&&(0,d.jsx)("div",{className:"space-y-8",children:q?(0,d.jsx)(Q,{}):(0,d.jsx)(C,{})}),"history"===a&&(0,d.jsx)(X,{}),"settings"===a&&(0,d.jsx)($,{})]})]})})]})}},79551:a=>{"use strict";a.exports=require("url")},80013:(a,b,c)=>{"use strict";c.d(b,{J:()=>g});var d=c(60687);c(43210);var e=c(78148),f=c(4780);function g({className:a,...b}){return(0,d.jsx)(e.b,{"data-slot":"label",className:(0,f.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...b})}},80559:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>g});var d=c(37413),e=c(97932),f=c(47178);function g(){return(0,d.jsx)(e.AuthGuard,{requireAuth:!0,children:(0,d.jsx)(f.DashboardContent,{})})}},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},89667:(a,b,c)=>{"use strict";c.d(b,{p:()=>f});var d=c(60687);c(43210);var e=c(4780);function f({className:a,type:b,...c}){return(0,d.jsx)("input",{type:b,"data-slot":"input",className:(0,e.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...c})}},94431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>k,metadata:()=>j});var d=c(37413),e=c(2202),f=c.n(e),g=c(64988),h=c.n(g);c(61135);var i=c(48482);let j={title:"饮食热量分析系统",description:"智能分析您的饮食热量，助您健康生活"};function k({children:a}){return(0,d.jsx)("html",{lang:"zh-CN",children:(0,d.jsxs)("body",{className:`${f().variable} ${h().variable} antialiased`,children:[a,(0,d.jsx)(i.Toaster,{})]})})}},94735:a=>{"use strict";a.exports=require("events")},97932:(a,b,c)=>{"use strict";c.d(b,{AuthGuard:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call AuthGuard() from the server but AuthGuard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/root/test/l-weight/app/src/components/auth/AuthGuard.tsx","AuthGuard")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[96],()=>b(b.s=10579));module.exports=c})();