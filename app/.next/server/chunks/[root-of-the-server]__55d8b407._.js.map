{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-anon-key';\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'placeholder-service-role-key';\n\n// 检查环境变量是否正确配置\nconst isConfigured =\n  process.env.NEXT_PUBLIC_SUPABASE_URL &&\n  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY &&\n  process.env.SUPABASE_SERVICE_ROLE_KEY &&\n  !process.env.NEXT_PUBLIC_SUPABASE_URL.includes('placeholder') &&\n  !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY.includes('placeholder') &&\n  !process.env.SUPABASE_SERVICE_ROLE_KEY.includes('placeholder');\n\n// 客户端 Supabase 实例（用于前端）\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// 服务端 Supabase 实例（用于服务端操作，具有更高权限）\nexport const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey);\n\n// 导出配置状态\nexport { isConfigured };\n\n// 数据库表名常量\nexport const TABLES = {\n  USERS: 'users',\n  USER_SETTINGS: 'user_settings',\n  DIET_RECORDS: 'diet_records',\n  INGREDIENTS: 'ingredients',\n  DISHES: 'dishes',\n} as const;\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,MAAM,cAAc,gFAAwC;AAC5D,MAAM,kBAAkB,wPAA6C;AACrE,MAAM,yBAAyB,QAAQ,GAAG,CAAC,yBAAyB,IAAI;AAExE,eAAe;AACf,MAAM,eACJ,wUAEA,QAAQ,GAAG,CAAC,yBAAyB,IACrC,CAAC,6EAAqC,QAAQ,CAAC,kBAC/C,CAAC,qPAA0C,QAAQ,CAAC,kBACpD,CAAC,QAAQ,GAAG,CAAC,yBAAyB,CAAC,QAAQ,CAAC;AAG3C,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;;AAMhD,MAAM,SAAS;IACpB,OAAO;IACP,eAAe;IACf,cAAc;IACd,aAAa;IACb,QAAQ;AACV", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/app/api/init-db/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { supabaseAdmin } from '@/lib/supabase';\nimport bcrypt from 'bcryptjs';\n\n// 初始化数据库\nexport async function POST(request: NextRequest) {\n  try {\n    // 直接尝试创建管理员用户\n    const adminUsername = process.env.ADMIN_USERNAME || 'wrhsd';\n    const adminPassword = process.env.ADMIN_PASSWORD || 'a123456';\n    const passwordHash = await bcrypt.hash(adminPassword, 12);\n\n    // 检查管理员用户是否已存在\n    const { data: existingAdmin } = await supabaseAdmin\n      .from('users')\n      .select('id')\n      .eq('username', adminUsername)\n      .single();\n\n    if (existingAdmin) {\n      return NextResponse.json({\n        success: true,\n        message: '管理员用户已存在',\n        data: {\n          adminUsername,\n          adminPassword,\n        },\n      });\n    }\n\n    // 创建管理员用户\n    const { data: newAdmin, error } = await supabaseAdmin\n      .from('users')\n      .insert({\n        username: adminUsername,\n        email: '<EMAIL>',\n        password_hash: passwordHash,\n        role: 'admin',\n      })\n      .select()\n      .single();\n\n    if (error) {\n      throw error;\n    }\n\n    return NextResponse.json({\n      success: true,\n      message: '管理员用户创建成功',\n      data: {\n        adminUsername,\n        adminPassword,\n        userId: newAdmin.id,\n      },\n    });\n\n  } catch (error: unknown) {\n    console.error('数据库初始化错误:', error);\n    const errorMessage = error instanceof Error ? error.message : '数据库初始化失败';\n\n    return NextResponse.json(\n      { success: false, error: errorMessage },\n      { status: 500 }\n    );\n  }\n}\n\n// 检查数据库状态\nexport async function GET() {\n  try {\n    // 简单的数据库连接测试\n    const { data, error } = await supabaseAdmin\n      .from('users')\n      .select('count')\n      .limit(1);\n\n    return NextResponse.json({\n      success: !error,\n      data: {\n        connected: !error,\n        timestamp: new Date().toISOString(),\n        error: error?.message,\n      },\n    });\n\n  } catch (error: unknown) {\n    console.error('数据库检查错误:', error);\n    const errorMessage = error instanceof Error ? error.message : '数据库检查失败';\n\n    return NextResponse.json(\n      { success: false, error: errorMessage },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,cAAc;QACd,MAAM,gBAAgB,QAAQ,GAAG,CAAC,cAAc,IAAI;QACpD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,cAAc,IAAI;QACpD,MAAM,eAAe,MAAM,gHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,eAAe;QAEtD,eAAe;QACf,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CAChD,IAAI,CAAC,SACL,MAAM,CAAC,MACP,EAAE,CAAC,YAAY,eACf,MAAM;QAET,IAAI,eAAe;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;gBACT,MAAM;oBACJ;oBACA;gBACF;YACF;QACF;QAEA,UAAU;QACV,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CAClD,IAAI,CAAC,SACL,MAAM,CAAC;YACN,UAAU;YACV,OAAO;YACP,eAAe;YACf,MAAM;QACR,GACC,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,MAAM;QACR;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,MAAM;gBACJ;gBACA;gBACA,QAAQ,SAAS,EAAE;YACrB;QACF;IAEF,EAAE,OAAO,OAAgB;QACvB,QAAQ,KAAK,CAAC,aAAa;QAC3B,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAE9D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAa,GACtC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe;IACpB,IAAI;QACF,aAAa;QACb,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CACxC,IAAI,CAAC,SACL,MAAM,CAAC,SACP,KAAK,CAAC;QAET,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS,CAAC;YACV,MAAM;gBACJ,WAAW,CAAC;gBACZ,WAAW,IAAI,OAAO,WAAW;gBACjC,OAAO,OAAO;YAChB;QACF;IAEF,EAAE,OAAO,OAAgB;QACvB,QAAQ,KAAK,CAAC,YAAY;QAC1B,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAE9D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAa,GACtC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}