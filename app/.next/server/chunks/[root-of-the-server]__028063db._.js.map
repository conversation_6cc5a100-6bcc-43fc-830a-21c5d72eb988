{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-anon-key';\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'placeholder-service-role-key';\n\n// 检查环境变量是否正确配置\nconst isConfigured =\n  process.env.NEXT_PUBLIC_SUPABASE_URL &&\n  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY &&\n  process.env.SUPABASE_SERVICE_ROLE_KEY &&\n  !process.env.NEXT_PUBLIC_SUPABASE_URL.includes('placeholder') &&\n  !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY.includes('placeholder') &&\n  !process.env.SUPABASE_SERVICE_ROLE_KEY.includes('placeholder');\n\n// 客户端 Supabase 实例（用于前端）\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// 服务端 Supabase 实例（用于服务端操作，具有更高权限）\nexport const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey);\n\n// 导出配置状态\nexport { isConfigured };\n\n// 数据库表名常量\nexport const TABLES = {\n  USERS: 'users',\n  USER_SETTINGS: 'user_settings',\n  DIET_RECORDS: 'diet_records',\n  INGREDIENTS: 'ingredients',\n  DISHES: 'dishes',\n} as const;\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,MAAM,cAAc,gFAAwC;AAC5D,MAAM,kBAAkB,wPAA6C;AACrE,MAAM,yBAAyB,QAAQ,GAAG,CAAC,yBAAyB,IAAI;AAExE,eAAe;AACf,MAAM,eACJ,wUAEA,QAAQ,GAAG,CAAC,yBAAyB,IACrC,CAAC,6EAAqC,QAAQ,CAAC,kBAC/C,CAAC,qPAA0C,QAAQ,CAAC,kBACpD,CAAC,QAAQ,GAAG,CAAC,yBAAyB,CAAC,QAAQ,CAAC;AAG3C,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;;AAMhD,MAAM,SAAS;IACpB,OAAO;IACP,eAAe;IACf,cAAc;IACd,aAAa;IACb,QAAQ;AACV", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/lib/init-db.ts"], "sourcesContent": ["import { supabaseAdmin } from './supabase';\nimport bcrypt from 'bcryptjs';\n\n// 初始化数据库\nexport async function initializeDatabase() {\n  try {\n    console.log('开始初始化数据库...');\n\n    // 检查管理员用户是否存在\n    const { data: existingAdmin } = await supabaseAdmin\n      .from('users')\n      .select('id')\n      .eq('username', process.env.ADMIN_USERNAME || 'wrhsd')\n      .single();\n\n    if (!existingAdmin) {\n      // 创建管理员用户\n      const adminPassword = process.env.ADMIN_PASSWORD || 'a123456';\n      const passwordHash = await bcrypt.hash(adminPassword, 12);\n\n      const { error: adminError } = await supabaseAdmin\n        .from('users')\n        .insert({\n          username: process.env.ADMIN_USERNAME || 'wrhsd',\n          email: '<EMAIL>',\n          password_hash: passwordHash,\n          role: 'admin',\n        });\n\n      if (adminError) {\n        console.error('创建管理员用户失败:', adminError);\n      } else {\n        console.log('管理员用户创建成功');\n      }\n    } else {\n      console.log('管理员用户已存在');\n    }\n\n    // 初始化一些基础菜品数据\n    const basicDishes = [\n      { name: '米饭', category: 'staple' },\n      { name: '面条', category: 'staple' },\n      { name: '馒头', category: 'staple' },\n      { name: '面包', category: 'staple' },\n      { name: '白开水', category: 'drink' },\n      { name: '茶', category: 'drink' },\n      { name: '咖啡', category: 'drink' },\n      { name: '牛奶', category: 'drink' },\n      { name: '鸡蛋', category: 'side' },\n      { name: '青菜', category: 'side' },\n    ];\n\n    const { error: dishError } = await supabaseAdmin\n      .from('dishes')\n      .upsert(basicDishes, { onConflict: 'name,category' });\n\n    if (dishError) {\n      console.error('初始化菜品数据失败:', dishError);\n    } else {\n      console.log('基础菜品数据初始化成功');\n    }\n\n    console.log('数据库初始化完成');\n  } catch (error) {\n    console.error('数据库初始化失败:', error);\n    throw error;\n  }\n}\n\n// 检查数据库连接\nexport async function checkDatabaseConnection() {\n  try {\n    const { error } = await supabaseAdmin\n      .from('users')\n      .select('count')\n      .limit(1);\n\n    if (error) {\n      console.error('数据库连接失败:', error);\n      return false;\n    }\n\n    console.log('数据库连接正常');\n    return true;\n  } catch (error) {\n    console.error('数据库连接检查失败:', error);\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;;;AAGO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,cAAc;QACd,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CAChD,IAAI,CAAC,SACL,MAAM,CAAC,MACP,EAAE,CAAC,YAAY,QAAQ,GAAG,CAAC,cAAc,IAAI,SAC7C,MAAM;QAET,IAAI,CAAC,eAAe;YAClB,UAAU;YACV,MAAM,gBAAgB,QAAQ,GAAG,CAAC,cAAc,IAAI;YACpD,MAAM,eAAe,MAAM,gHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,eAAe;YAEtD,MAAM,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CAC9C,IAAI,CAAC,SACL,MAAM,CAAC;gBACN,UAAU,QAAQ,GAAG,CAAC,cAAc,IAAI;gBACxC,OAAO;gBACP,eAAe;gBACf,MAAM;YACR;YAEF,IAAI,YAAY;gBACd,QAAQ,KAAK,CAAC,cAAc;YAC9B,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;QACF,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;QAEA,cAAc;QACd,MAAM,cAAc;YAClB;gBAAE,MAAM;gBAAM,UAAU;YAAS;YACjC;gBAAE,MAAM;gBAAM,UAAU;YAAS;YACjC;gBAAE,MAAM;gBAAM,UAAU;YAAS;YACjC;gBAAE,MAAM;gBAAM,UAAU;YAAS;YACjC;gBAAE,MAAM;gBAAO,UAAU;YAAQ;YACjC;gBAAE,MAAM;gBAAK,UAAU;YAAQ;YAC/B;gBAAE,MAAM;gBAAM,UAAU;YAAQ;YAChC;gBAAE,MAAM;gBAAM,UAAU;YAAQ;YAChC;gBAAE,MAAM;gBAAM,UAAU;YAAO;YAC/B;gBAAE,MAAM;gBAAM,UAAU;YAAO;SAChC;QAED,MAAM,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CAC7C,IAAI,CAAC,UACL,MAAM,CAAC,aAAa;YAAE,YAAY;QAAgB;QAErD,IAAI,WAAW;YACb,QAAQ,KAAK,CAAC,cAAc;QAC9B,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;QAEA,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,MAAM;IACR;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CAClC,IAAI,CAAC,SACL,MAAM,CAAC,SACP,KAAK,CAAC;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,YAAY;YAC1B,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/app/api/init-db/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { initializeDatabase, checkDatabaseConnection } from '@/lib/init-db';\n\n// 初始化数据库\nexport async function POST(request: NextRequest) {\n  try {\n    // 检查数据库连接\n    const isConnected = await checkDatabaseConnection();\n    if (!isConnected) {\n      return NextResponse.json(\n        { success: false, error: '数据库连接失败，请检查配置' },\n        { status: 500 }\n      );\n    }\n\n    // 初始化数据库\n    await initializeDatabase();\n\n    return NextResponse.json({\n      success: true,\n      message: '数据库初始化成功，管理员账号已创建',\n      data: {\n        adminUsername: process.env.ADMIN_USERNAME || 'wrhsd',\n        adminPassword: process.env.ADMIN_PASSWORD || 'a123456',\n      },\n    });\n\n  } catch (error: unknown) {\n    console.error('数据库初始化错误:', error);\n    const errorMessage = error instanceof Error ? error.message : '数据库初始化失败';\n    \n    return NextResponse.json(\n      { success: false, error: errorMessage },\n      { status: 500 }\n    );\n  }\n}\n\n// 检查数据库状态\nexport async function GET() {\n  try {\n    const isConnected = await checkDatabaseConnection();\n    \n    return NextResponse.json({\n      success: true,\n      data: {\n        connected: isConnected,\n        timestamp: new Date().toISOString(),\n      },\n    });\n\n  } catch (error: unknown) {\n    console.error('数据库检查错误:', error);\n    const errorMessage = error instanceof Error ? error.message : '数据库检查失败';\n    \n    return NextResponse.json(\n      { success: false, error: errorMessage },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,UAAU;QACV,MAAM,cAAc,MAAM,CAAA,GAAA,0HAAA,CAAA,0BAAuB,AAAD;QAChD,IAAI,CAAC,aAAa;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAgB,GACzC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD;QAEvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,MAAM;gBACJ,eAAe,QAAQ,GAAG,CAAC,cAAc,IAAI;gBAC7C,eAAe,QAAQ,GAAG,CAAC,cAAc,IAAI;YAC/C;QACF;IAEF,EAAE,OAAO,OAAgB;QACvB,QAAQ,KAAK,CAAC,aAAa;QAC3B,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAE9D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAa,GACtC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,cAAc,MAAM,CAAA,GAAA,0HAAA,CAAA,0BAAuB,AAAD;QAEhD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,WAAW;gBACX,WAAW,IAAI,OAAO,WAAW;YACnC;QACF;IAEF,EAAE,OAAO,OAAgB;QACvB,QAAQ,KAAK,CAAC,YAAY;QAC1B,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAE9D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAa,GACtC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}