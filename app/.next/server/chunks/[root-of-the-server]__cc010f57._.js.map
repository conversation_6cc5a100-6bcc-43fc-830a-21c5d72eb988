{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-anon-key';\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'placeholder-service-role-key';\n\n// 检查环境变量是否正确配置\nconst isConfigured =\n  process.env.NEXT_PUBLIC_SUPABASE_URL &&\n  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY &&\n  process.env.SUPABASE_SERVICE_ROLE_KEY &&\n  !process.env.NEXT_PUBLIC_SUPABASE_URL.includes('placeholder') &&\n  !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY.includes('placeholder') &&\n  !process.env.SUPABASE_SERVICE_ROLE_KEY.includes('placeholder');\n\n// 客户端 Supabase 实例（用于前端）\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// 服务端 Supabase 实例（用于服务端操作，具有更高权限）\nexport const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey);\n\n// 导出配置状态\nexport { isConfigured };\n\n// 数据库表名常量\nexport const TABLES = {\n  USERS: 'users',\n  USER_SETTINGS: 'user_settings',\n  DIET_RECORDS: 'diet_records',\n  INGREDIENTS: 'ingredients',\n  DISHES: 'dishes',\n} as const;\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,MAAM,cAAc,gFAAwC;AAC5D,MAAM,kBAAkB,wPAA6C;AACrE,MAAM,yBAAyB,QAAQ,GAAG,CAAC,yBAAyB,IAAI;AAExE,eAAe;AACf,MAAM,eACJ,wUAEA,QAAQ,GAAG,CAAC,yBAAyB,IACrC,CAAC,6EAAqC,QAAQ,CAAC,kBAC/C,CAAC,qPAA0C,QAAQ,CAAC,kBACpD,CAAC,QAAQ,GAAG,CAAC,yBAAyB,CAAC,QAAQ,CAAC;AAG3C,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;;AAMhD,MAAM,SAAS;IACpB,OAAO;IACP,eAAe;IACf,cAAc;IACd,aAAa;IACb,QAAQ;AACV", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/lib/database.ts"], "sourcesContent": ["import { supabase, supabaseAdmin, TABLES, isConfigured } from './supabase';\nimport { User, UserSettings, DietRecord, Ingredient, Dish } from '@/types';\nimport bcrypt from 'bcryptjs';\n\n// 用户相关操作\nexport class UserService {\n  // 创建用户\n  static async createUser(userData: {\n    username: string;\n    email: string;\n    password: string;\n    role?: 'admin' | 'user';\n    auth_code?: string;\n  }): Promise<User> {\n    if (!isConfigured) {\n      throw new Error('数据库未配置，请设置正确的环境变量');\n    }\n    const passwordHash = await bcrypt.hash(userData.password, 12);\n\n    const { data, error } = await supabaseAdmin\n      .from(TABLES.USERS)\n      .insert({\n        username: userData.username,\n        email: userData.email,\n        password_hash: passwordHash,\n        role: userData.role || 'user',\n        auth_code: userData.auth_code,\n      })\n      .select('id, username, email, role, auth_code, created_at, updated_at')\n      .single();\n\n    if (error) throw error;\n    return data;\n  }\n\n  // 验证用户登录\n  static async validateUser(username: string, password: string): Promise<User | null> {\n    const { data, error } = await supabaseAdmin\n      .from(TABLES.USERS)\n      .select('*')\n      .eq('username', username)\n      .single();\n\n    if (error || !data) return null;\n\n    const isValid = await bcrypt.compare(password, data.password_hash);\n    if (!isValid) return null;\n\n    // 返回不包含密码的用户信息\n    const { password_hash: _, ...userInfo } = data;\n    return userInfo as User;\n  }\n\n  // 获取用户信息\n  static async getUserById(id: string): Promise<User | null> {\n    const { data, error } = await supabase\n      .from(TABLES.USERS)\n      .select('id, username, email, role, auth_code, created_at, updated_at')\n      .eq('id', id)\n      .single();\n\n    if (error) return null;\n    return data;\n  }\n\n  // 获取所有用户（管理员功能）\n  static async getAllUsers(): Promise<User[]> {\n    const { data, error } = await supabaseAdmin\n      .from(TABLES.USERS)\n      .select('id, username, email, role, auth_code, created_at, updated_at')\n      .order('created_at', { ascending: false });\n\n    if (error) throw error;\n    return data || [];\n  }\n\n  // 更新用户信息\n  static async updateUser(id: string, updates: Partial<User>): Promise<User> {\n    const { data, error } = await supabaseAdmin\n      .from(TABLES.USERS)\n      .update(updates)\n      .eq('id', id)\n      .select('id, username, email, role, auth_code, created_at, updated_at')\n      .single();\n\n    if (error) throw error;\n    return data;\n  }\n\n  // 重置用户密码\n  static async resetPassword(id: string, newPassword: string): Promise<void> {\n    const passwordHash = await bcrypt.hash(newPassword, 12);\n    \n    const { error } = await supabaseAdmin\n      .from(TABLES.USERS)\n      .update({ password_hash: passwordHash })\n      .eq('id', id);\n\n    if (error) throw error;\n  }\n\n  // 删除用户\n  static async deleteUser(id: string): Promise<void> {\n    const { error } = await supabaseAdmin\n      .from(TABLES.USERS)\n      .delete()\n      .eq('id', id);\n\n    if (error) throw error;\n  }\n}\n\n// 用户设置相关操作\nexport class UserSettingsService {\n  // 获取用户设置\n  static async getUserSettings(userId: string): Promise<UserSettings | null> {\n    const { data, error } = await supabase\n      .from(TABLES.USER_SETTINGS)\n      .select('*')\n      .eq('user_id', userId)\n      .single();\n\n    if (error) return null;\n    return data;\n  }\n\n  // 更新用户设置\n  static async updateUserSettings(userId: string, settings: Partial<UserSettings>): Promise<UserSettings> {\n    const { data, error } = await supabase\n      .from(TABLES.USER_SETTINGS)\n      .upsert({\n        user_id: userId,\n        ...settings,\n      })\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data;\n  }\n}\n\n// 饮食记录相关操作\nexport class DietRecordService {\n  // 创建饮食记录\n  static async createDietRecord(recordData: {\n    user_id: string;\n    original_input: string;\n    input_type: 'text' | 'image';\n    image_url?: string;\n    meal_type: string;\n    record_time?: string;\n    total_calories: number;\n    ai_analysis: string;\n    dishes: string[];\n    ingredients: Omit<Ingredient, 'id' | 'diet_record_id' | 'created_at'>[];\n  }): Promise<DietRecord> {\n    // 创建饮食记录\n    const { data: record, error: recordError } = await supabase\n      .from(TABLES.DIET_RECORDS)\n      .insert({\n        user_id: recordData.user_id,\n        original_input: recordData.original_input,\n        input_type: recordData.input_type,\n        image_url: recordData.image_url,\n        meal_type: recordData.meal_type,\n        record_time: recordData.record_time,\n        total_calories: recordData.total_calories,\n        ai_analysis: recordData.ai_analysis,\n        dishes: recordData.dishes,\n      })\n      .select()\n      .single();\n\n    if (recordError) throw recordError;\n\n    // 创建食材记录\n    if (recordData.ingredients.length > 0) {\n      const ingredientsData = recordData.ingredients.map(ingredient => ({\n        ...ingredient,\n        diet_record_id: record.id,\n      }));\n\n      const { error: ingredientsError } = await supabase\n        .from(TABLES.INGREDIENTS)\n        .insert(ingredientsData);\n\n      if (ingredientsError) throw ingredientsError;\n    }\n\n    return record;\n  }\n\n  // 获取用户的饮食记录\n  static async getUserDietRecords(userId: string, limit = 50, offset = 0): Promise<DietRecord[]> {\n    const { data, error } = await supabase\n      .from(TABLES.DIET_RECORDS)\n      .select(`\n        *,\n        ingredients (*)\n      `)\n      .eq('user_id', userId)\n      .order('record_time', { ascending: false })\n      .range(offset, offset + limit - 1);\n\n    if (error) throw error;\n    return data || [];\n  }\n\n  // 删除饮食记录\n  static async deleteDietRecord(id: string, userId: string): Promise<void> {\n    const { error } = await supabase\n      .from(TABLES.DIET_RECORDS)\n      .delete()\n      .eq('id', id)\n      .eq('user_id', userId);\n\n    if (error) throw error;\n  }\n}\n\n// 菜品库相关操作\nexport class DishService {\n  // 添加菜品到库中\n  static async addDishes(dishes: { name: string; category: string }[]): Promise<void> {\n    const { error } = await supabase\n      .from(TABLES.DISHES)\n      .upsert(dishes, { onConflict: 'name,category' });\n\n    if (error) throw error;\n  }\n\n  // 获取所有菜品\n  static async getAllDishes(): Promise<Dish[]> {\n    const { data, error } = await supabase\n      .from(TABLES.DISHES)\n      .select('*')\n      .order('name');\n\n    if (error) throw error;\n    return data || [];\n  }\n\n  // 搜索菜品\n  static async searchDishes(query: string): Promise<Dish[]> {\n    const { data, error } = await supabase\n      .from(TABLES.DISHES)\n      .select('*')\n      .ilike('name', `%${query}%`)\n      .order('name')\n      .limit(20);\n\n    if (error) throw error;\n    return data || [];\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AAEA;;;;;;;AAGO,MAAM;IACX,OAAO;IACP,aAAa,WAAW,QAMvB,EAAiB;QAChB,IAAI,CAAC,wHAAA,CAAA,eAAY,EAAE;YACjB,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,eAAe,MAAM,gHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,SAAS,QAAQ,EAAE;QAE1D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CACxC,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CAAC;YACN,UAAU,SAAS,QAAQ;YAC3B,OAAO,SAAS,KAAK;YACrB,eAAe;YACf,MAAM,SAAS,IAAI,IAAI;YACvB,WAAW,SAAS,SAAS;QAC/B,GACC,MAAM,CAAC,gEACP,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,SAAS;IACT,aAAa,aAAa,QAAgB,EAAE,QAAgB,EAAwB;QAClF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CACxC,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CAAC,KACP,EAAE,CAAC,YAAY,UACf,MAAM;QAET,IAAI,SAAS,CAAC,MAAM,OAAO;QAE3B,MAAM,UAAU,MAAM,gHAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU,KAAK,aAAa;QACjE,IAAI,CAAC,SAAS,OAAO;QAErB,eAAe;QACf,MAAM,EAAE,eAAe,CAAC,EAAE,GAAG,UAAU,GAAG;QAC1C,OAAO;IACT;IAEA,SAAS;IACT,aAAa,YAAY,EAAU,EAAwB;QACzD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CAAC,gEACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO,OAAO;QAClB,OAAO;IACT;IAEA,gBAAgB;IAChB,aAAa,cAA+B;QAC1C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CACxC,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CAAC,gEACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;IAEA,SAAS;IACT,aAAa,WAAW,EAAU,EAAE,OAAsB,EAAiB;QACzE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CACxC,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,CAAC,gEACP,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,SAAS;IACT,aAAa,cAAc,EAAU,EAAE,WAAmB,EAAiB;QACzE,MAAM,eAAe,MAAM,gHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,aAAa;QAEpD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CAClC,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CAAC;YAAE,eAAe;QAAa,GACrC,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;IAEA,OAAO;IACP,aAAa,WAAW,EAAU,EAAiB;QACjD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,gBAAa,CAClC,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;AACF;AAGO,MAAM;IACX,SAAS;IACT,aAAa,gBAAgB,MAAc,EAAgC;QACzE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,aAAa,EACzB,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,QACd,MAAM;QAET,IAAI,OAAO,OAAO;QAClB,OAAO;IACT;IAEA,SAAS;IACT,aAAa,mBAAmB,MAAc,EAAE,QAA+B,EAAyB;QACtG,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,aAAa,EACzB,MAAM,CAAC;YACN,SAAS;YACT,GAAG,QAAQ;QACb,GACC,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF;AAGO,MAAM;IACX,SAAS;IACT,aAAa,iBAAiB,UAW7B,EAAuB;QACtB,SAAS;QACT,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACxD,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,YAAY,EACxB,MAAM,CAAC;YACN,SAAS,WAAW,OAAO;YAC3B,gBAAgB,WAAW,cAAc;YACzC,YAAY,WAAW,UAAU;YACjC,WAAW,WAAW,SAAS;YAC/B,WAAW,WAAW,SAAS;YAC/B,aAAa,WAAW,WAAW;YACnC,gBAAgB,WAAW,cAAc;YACzC,aAAa,WAAW,WAAW;YACnC,QAAQ,WAAW,MAAM;QAC3B,GACC,MAAM,GACN,MAAM;QAET,IAAI,aAAa,MAAM;QAEvB,SAAS;QACT,IAAI,WAAW,WAAW,CAAC,MAAM,GAAG,GAAG;YACrC,MAAM,kBAAkB,WAAW,WAAW,CAAC,GAAG,CAAC,CAAA,aAAc,CAAC;oBAChE,GAAG,UAAU;oBACb,gBAAgB,OAAO,EAAE;gBAC3B,CAAC;YAED,MAAM,EAAE,OAAO,gBAAgB,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC/C,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,WAAW,EACvB,MAAM,CAAC;YAEV,IAAI,kBAAkB,MAAM;QAC9B;QAEA,OAAO;IACT;IAEA,YAAY;IACZ,aAAa,mBAAmB,MAAc,EAAE,QAAQ,EAAE,EAAE,SAAS,CAAC,EAAyB;QAC7F,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,YAAY,EACxB,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,EAAE,CAAC,WAAW,QACd,KAAK,CAAC,eAAe;YAAE,WAAW;QAAM,GACxC,KAAK,CAAC,QAAQ,SAAS,QAAQ;QAElC,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;IAEA,SAAS;IACT,aAAa,iBAAiB,EAAU,EAAE,MAAc,EAAiB;QACvE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,YAAY,EACxB,MAAM,GACN,EAAE,CAAC,MAAM,IACT,EAAE,CAAC,WAAW;QAEjB,IAAI,OAAO,MAAM;IACnB;AACF;AAGO,MAAM;IACX,UAAU;IACV,aAAa,UAAU,MAA4C,EAAiB;QAClF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,MAAM,EAClB,MAAM,CAAC,QAAQ;YAAE,YAAY;QAAgB;QAEhD,IAAI,OAAO,MAAM;IACnB;IAEA,SAAS;IACT,aAAa,eAAgC;QAC3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,MAAM,EAClB,MAAM,CAAC,KACP,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;IAEA,OAAO;IACP,aAAa,aAAa,KAAa,EAAmB;QACxD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,wHAAA,CAAA,SAAM,CAAC,MAAM,EAClB,MAAM,CAAC,KACP,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAC1B,KAAK,CAAC,QACN,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;AACF", "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/app/api/admin/users/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { UserService } from '@/lib/database';\nimport { z } from 'zod';\n\n// 验证管理员权限的中间件函数\nasync function verifyAdmin(request: NextRequest) {\n  const authHeader = request.headers.get('authorization');\n  if (!authHeader) {\n    return null;\n  }\n\n  // 这里简化处理，实际应该验证JWT token\n  // 暂时通过请求头中的用户信息验证\n  try {\n    const userInfo = JSON.parse(authHeader.replace('Bearer ', ''));\n    if (userInfo.role !== 'admin') {\n      return null;\n    }\n    return userInfo;\n  } catch {\n    return null;\n  }\n}\n\n// 获取所有用户\nexport async function GET(request: NextRequest) {\n  try {\n    const admin = await verifyAdmin(request);\n    if (!admin) {\n      return NextResponse.json(\n        { success: false, error: '权限不足' },\n        { status: 403 }\n      );\n    }\n\n    const users = await UserService.getAllUsers();\n\n    return NextResponse.json({\n      success: true,\n      data: users,\n    });\n\n  } catch (error) {\n    console.error('获取用户列表错误:', error);\n    return NextResponse.json(\n      { success: false, error: '获取用户列表失败' },\n      { status: 500 }\n    );\n  }\n}\n\nconst createUserSchema = z.object({\n  username: z.string().min(3, '用户名至少3个字符').max(50, '用户名最多50个字符'),\n  email: z.string().email('邮箱格式不正确'),\n  password: z.string().min(6, '密码至少6个字符'),\n  role: z.enum(['admin', 'user']),\n  auth_code: z.string().optional(),\n});\n\n// 创建用户\nexport async function POST(request: NextRequest) {\n  try {\n    const admin = await verifyAdmin(request);\n    if (!admin) {\n      return NextResponse.json(\n        { success: false, error: '权限不足' },\n        { status: 403 }\n      );\n    }\n\n    const body = await request.json();\n    const userData = createUserSchema.parse(body);\n\n    const user = await UserService.createUser(userData);\n\n    return NextResponse.json({\n      success: true,\n      data: user,\n      message: '用户创建成功',\n    });\n\n  } catch (error: unknown) {\n    console.error('创建用户错误:', error);\n    \n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { success: false, error: error.issues[0].message },\n        { status: 400 }\n      );\n    }\n\n    // 处理数据库唯一约束错误\n    if (error && typeof error === 'object' && 'code' in error && error.code === '23505') {\n      const dbError = error as { constraint?: string };\n      if (dbError.constraint?.includes('username')) {\n        return NextResponse.json(\n          { success: false, error: '用户名已存在' },\n          { status: 409 }\n        );\n      }\n      if (dbError.constraint?.includes('email')) {\n        return NextResponse.json(\n          { success: false, error: '邮箱已被注册' },\n          { status: 409 }\n        );\n      }\n    }\n\n    return NextResponse.json(\n      { success: false, error: '创建用户失败' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;;;;AAEA,gBAAgB;AAChB,eAAe,YAAY,OAAoB;IAC7C,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,IAAI,CAAC,YAAY;QACf,OAAO;IACT;IAEA,yBAAyB;IACzB,kBAAkB;IAClB,IAAI;QACF,MAAM,WAAW,KAAK,KAAK,CAAC,WAAW,OAAO,CAAC,WAAW;QAC1D,IAAI,SAAS,IAAI,KAAK,SAAS;YAC7B,OAAO;QACT;QACA,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,QAAQ,MAAM,YAAY;QAChC,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAO,GAChC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,QAAQ,MAAM,wHAAA,CAAA,cAAW,CAAC,WAAW;QAE3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAW,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,MAAM,mBAAmB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC,IAAI;IACjD,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,MAAM,+KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAS;KAAO;IAC9B,WAAW,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAChC;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,QAAQ,MAAM,YAAY;QAChC,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAO,GAChC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,WAAW,iBAAiB,KAAK,CAAC;QAExC,MAAM,OAAO,MAAM,wHAAA,CAAA,cAAW,CAAC,UAAU,CAAC;QAE1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAgB;QACvB,QAAQ,KAAK,CAAC,WAAW;QAEzB,IAAI,iBAAiB,+KAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO,MAAM,MAAM,CAAC,EAAE,CAAC,OAAO;YAAC,GACjD;gBAAE,QAAQ;YAAI;QAElB;QAEA,cAAc;QACd,IAAI,SAAS,OAAO,UAAU,YAAY,UAAU,SAAS,MAAM,IAAI,KAAK,SAAS;YACnF,MAAM,UAAU;YAChB,IAAI,QAAQ,UAAU,EAAE,SAAS,aAAa;gBAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,OAAO;gBAAS,GAClC;oBAAE,QAAQ;gBAAI;YAElB;YACA,IAAI,QAAQ,UAAU,EAAE,SAAS,UAAU;gBACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,OAAO;gBAAS,GAClC;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAS,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}