{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/store/useAuthStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { User } from '@/types';\n\ninterface AuthState {\n  user: User | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  login: (user: User) => void;\n  logout: () => void;\n  setLoading: (loading: boolean) => void;\n  updateUser: (user: Partial<User>) => void;\n}\n\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set, get) => ({\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n      \n      login: (user: User) => {\n        set({ user, isAuthenticated: true, isLoading: false });\n      },\n      \n      logout: () => {\n        set({ user: null, isAuthenticated: false, isLoading: false });\n      },\n      \n      setLoading: (loading: boolean) => {\n        set({ isLoading: loading });\n      },\n      \n      updateUser: (userData: Partial<User>) => {\n        const currentUser = get().user;\n        if (currentUser) {\n          set({ user: { ...currentUser, ...userData } });\n        }\n      },\n    }),\n    {\n      name: 'auth-storage',\n      partialize: (state) => ({\n        user: state.user,\n        isAuthenticated: state.isAuthenticated,\n      }),\n    }\n  )\n);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAaO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,iBAAiB;QACjB,WAAW;QAEX,OAAO,CAAC;YACN,IAAI;gBAAE;gBAAM,iBAAiB;gBAAM,WAAW;YAAM;QACtD;QAEA,QAAQ;YACN,IAAI;gBAAE,MAAM;gBAAM,iBAAiB;gBAAO,WAAW;YAAM;QAC7D;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE,WAAW;YAAQ;QAC3B;QAEA,YAAY,CAAC;YACX,MAAM,cAAc,MAAM,IAAI;YAC9B,IAAI,aAAa;gBACf,IAAI;oBAAE,MAAM;wBAAE,GAAG,WAAW;wBAAE,GAAG,QAAQ;oBAAC;gBAAE;YAC9C;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH", "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/auth/AuthGuard.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuthStore } from '@/store/useAuthStore';\n\ninterface AuthGuardProps {\n  children: React.ReactNode;\n  requireAuth?: boolean;\n  requireAdmin?: boolean;\n  redirectTo?: string;\n}\n\nexport function AuthGuard({ \n  children, \n  requireAuth = true, \n  requireAdmin = false,\n  redirectTo \n}: AuthGuardProps) {\n  const { isAuthenticated, user, isLoading } = useAuthStore();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (isLoading) return;\n\n    if (requireAuth && !isAuthenticated) {\n      router.push(redirectTo || '/login');\n      return;\n    }\n\n    if (requireAdmin && user?.role !== 'admin') {\n      router.push('/dashboard');\n      return;\n    }\n\n    if (!requireAuth && isAuthenticated) {\n      router.push('/dashboard');\n      return;\n    }\n  }, [isAuthenticated, user, isLoading, requireAuth, requireAdmin, router, redirectTo]);\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900\"></div>\n      </div>\n    );\n  }\n\n  if (requireAuth && !isAuthenticated) {\n    return null;\n  }\n\n  if (requireAdmin && user?.role !== 'admin') {\n    return null;\n  }\n\n  if (!requireAuth && isAuthenticated) {\n    return null;\n  }\n\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAaO,SAAS,UAAU,EACxB,QAAQ,EACR,cAAc,IAAI,EAClB,eAAe,KAAK,EACpB,UAAU,EACK;IACf,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IACxD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;QAEf,IAAI,eAAe,CAAC,iBAAiB;YACnC,OAAO,IAAI,CAAC,cAAc;YAC1B;QACF;QAEA,IAAI,gBAAgB,MAAM,SAAS,SAAS;YAC1C,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,eAAe,iBAAiB;YACnC,OAAO,IAAI,CAAC;YACZ;QACF;IACF,GAAG;QAAC;QAAiB;QAAM;QAAW;QAAa;QAAc;QAAQ;KAAW;IAEpF,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,eAAe,CAAC,iBAAiB;QACnC,OAAO;IACT;IAEA,IAAI,gBAAgB,MAAM,SAAS,SAAS;QAC1C,OAAO;IACT;IAEA,IAAI,CAAC,eAAe,iBAAiB;QACnC,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 316, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/store/useDietStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { DietRecord, AIAnalysisResult, CalorieAnalysisResult } from '@/types';\n\ninterface DietState {\n  // 当前分析的饮食记录\n  currentInput: string;\n  currentInputType: 'text' | 'image';\n  currentImageUrl?: string;\n  \n  // 分析结果\n  analysisResult: AIAnalysisResult | null;\n  calorieResult: CalorieAnalysisResult | null;\n  \n  // 历史记录\n  dietRecords: DietRecord[];\n  \n  // 加载状态\n  isAnalyzing: boolean;\n  isSubmitting: boolean;\n  \n  // 操作方法\n  setCurrentInput: (input: string, type: 'text' | 'image', imageUrl?: string) => void;\n  setAnalysisResult: (result: AIAnalysisResult | null) => void;\n  setCalorieResult: (result: CalorieAnalysisResult | null) => void;\n  setDietRecords: (records: DietRecord[]) => void;\n  addDietRecord: (record: DietRecord) => void;\n  removeDietRecord: (id: string) => void;\n  setIsAnalyzing: (isAnalyzing: boolean) => void;\n  setIsSubmitting: (isSubmitting: boolean) => void;\n  reset: () => void;\n}\n\nexport const useDietStore = create<DietState>((set) => ({\n  // 初始状态\n  currentInput: '',\n  currentInputType: 'text',\n  currentImageUrl: undefined,\n  analysisResult: null,\n  calorieResult: null,\n  dietRecords: [],\n  isAnalyzing: false,\n  isSubmitting: false,\n  \n  // 方法\n  setCurrentInput: (input, type, imageUrl) => set({ \n    currentInput: input, \n    currentInputType: type,\n    currentImageUrl: imageUrl\n  }),\n  \n  setAnalysisResult: (result) => set({ analysisResult: result }),\n  \n  setCalorieResult: (result) => set({ calorieResult: result }),\n  \n  setDietRecords: (records) => set({ dietRecords: records }),\n  \n  addDietRecord: (record) => set((state) => ({ \n    dietRecords: [record, ...state.dietRecords] \n  })),\n  \n  removeDietRecord: (id) => set((state) => ({ \n    dietRecords: state.dietRecords.filter(record => record.id !== id) \n  })),\n  \n  setIsAnalyzing: (isAnalyzing) => set({ isAnalyzing }),\n  \n  setIsSubmitting: (isSubmitting) => set({ isSubmitting }),\n  \n  reset: () => set({ \n    currentInput: '',\n    currentInputType: 'text',\n    currentImageUrl: undefined,\n    analysisResult: null,\n    calorieResult: null,\n    isAnalyzing: false,\n    isSubmitting: false\n  })\n}));\n"], "names": [], "mappings": ";;;AAAA;;AAgCO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAa,CAAC,MAAQ,CAAC;QACtD,OAAO;QACP,cAAc;QACd,kBAAkB;QAClB,iBAAiB;QACjB,gBAAgB;QAChB,eAAe;QACf,aAAa,EAAE;QACf,aAAa;QACb,cAAc;QAEd,KAAK;QACL,iBAAiB,CAAC,OAAO,MAAM,WAAa,IAAI;gBAC9C,cAAc;gBACd,kBAAkB;gBAClB,iBAAiB;YACnB;QAEA,mBAAmB,CAAC,SAAW,IAAI;gBAAE,gBAAgB;YAAO;QAE5D,kBAAkB,CAAC,SAAW,IAAI;gBAAE,eAAe;YAAO;QAE1D,gBAAgB,CAAC,UAAY,IAAI;gBAAE,aAAa;YAAQ;QAExD,eAAe,CAAC,SAAW,IAAI,CAAC,QAAU,CAAC;oBACzC,aAAa;wBAAC;2BAAW,MAAM,WAAW;qBAAC;gBAC7C,CAAC;QAED,kBAAkB,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBACxC,aAAa,MAAM,WAAW,CAAC,MAAM,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;gBAChE,CAAC;QAED,gBAAgB,CAAC,cAAgB,IAAI;gBAAE;YAAY;QAEnD,iBAAiB,CAAC,eAAiB,IAAI;gBAAE;YAAa;QAEtD,OAAO,IAAM,IAAI;gBACf,cAAc;gBACd,kBAAkB;gBAClB,iBAAiB;gBACjB,gBAAgB;gBAChB,eAAe;gBACf,aAAa;gBACb,cAAc;YAChB;IACF,CAAC", "debugId": null}}, {"offset": {"line": 479, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 503, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 552, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/diet/DietInputForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { toast } from 'sonner';\nimport axios from 'axios';\nimport Image from 'next/image';\n\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Camera, Upload, Loader2 } from 'lucide-react';\nimport { useAuthStore } from '@/store/useAuthStore';\nimport { useDietStore } from '@/store/useDietStore';\n\nconst dietInputSchema = z.object({\n  input: z.string().min(1, '请输入饮食内容或上传图片'),\n});\n\ntype DietInputFormData = z.infer<typeof dietInputSchema>;\n\nexport function DietInputForm() {\n  const [inputType, setInputType] = useState<'text' | 'image'>('text');\n  const [selectedImage, setSelectedImage] = useState<File | null>(null);\n  const [imagePreview, setImagePreview] = useState<string | null>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  \n  const { user } = useAuthStore();\n  const { setCurrentInput, setIsAnalyzing, isAnalyzing } = useDietStore();\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    reset,\n  } = useForm<DietInputFormData>({\n    resolver: zodResolver(dietInputSchema),\n  });\n\n  // 处理图片选择\n  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (file) {\n      // 验证文件类型\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n      if (!allowedTypes.includes(file.type)) {\n        toast.error('只支持 JPEG、PNG、WebP 格式的图片');\n        return;\n      }\n\n      // 验证文件大小 (10MB)\n      const maxSize = 10 * 1024 * 1024;\n      if (file.size > maxSize) {\n        toast.error('图片大小不能超过 10MB');\n        return;\n      }\n\n      setSelectedImage(file);\n      \n      // 创建预览\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setImagePreview(e.target?.result as string);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  // 提交表单\n  const onSubmit = async (data: DietInputFormData) => {\n    if (!user) {\n      toast.error('请先登录');\n      return;\n    }\n\n    setIsAnalyzing(true);\n\n    try {\n      let imageUrl: string | undefined;\n\n      // 如果是图片输入，先上传图片\n      if (inputType === 'image' && selectedImage) {\n        const formData = new FormData();\n        formData.append('file', selectedImage);\n\n        const uploadResponse = await axios.post('/api/upload/image', formData, {\n          headers: {\n            'Authorization': `Bearer ${JSON.stringify(user)}`,\n            'Content-Type': 'multipart/form-data',\n          },\n        });\n\n        if (uploadResponse.data.success) {\n          imageUrl = uploadResponse.data.data.thumbnailUrl;\n        } else {\n          throw new Error('图片上传失败');\n        }\n      }\n\n      // 调用AI分析\n      const analysisResponse = await axios.post('/api/ai/analyze-diet', {\n        input: data.input,\n        inputType,\n        imageUrl,\n      }, {\n        headers: {\n          'Authorization': `Bearer ${JSON.stringify(user)}`,\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (analysisResponse.data.success) {\n        // 保存分析结果到store\n        setCurrentInput(data.input, inputType, imageUrl);\n        useDietStore.getState().setAnalysisResult(analysisResponse.data.data);\n        \n        toast.success('分析完成');\n        \n        // 重置表单\n        reset();\n        setSelectedImage(null);\n        setImagePreview(null);\n        setInputType('text');\n      } else {\n        throw new Error(analysisResponse.data.error || '分析失败');\n      }\n\n    } catch (error: unknown) {\n      console.error('饮食分析错误:', error);\n      const axiosError = error as { response?: { data?: { error?: string } } };\n      toast.error(axiosError.response?.data?.error || '分析失败，请稍后重试');\n    } finally {\n      setIsAnalyzing(false);\n    }\n  };\n\n  return (\n    <Card className=\"w-full max-w-2xl mx-auto\">\n      <CardHeader>\n        <CardTitle>饮食记录</CardTitle>\n        <CardDescription>\n          输入您的饮食内容或上传食物图片，AI将为您分析食材和热量\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n          {/* 输入类型选择 */}\n          <div className=\"flex gap-4\">\n            <Button\n              type=\"button\"\n              variant={inputType === 'text' ? 'default' : 'outline'}\n              onClick={() => setInputType('text')}\n              className=\"flex-1\"\n            >\n              文字输入\n            </Button>\n            <Button\n              type=\"button\"\n              variant={inputType === 'image' ? 'default' : 'outline'}\n              onClick={() => setInputType('image')}\n              className=\"flex-1\"\n            >\n              <Camera className=\"w-4 h-4 mr-2\" />\n              图片上传\n            </Button>\n          </div>\n\n          {/* 文字输入 */}\n          {inputType === 'text' && (\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"input\">饮食描述</Label>\n              <Textarea\n                id=\"input\"\n                placeholder=\"请描述您吃了什么，例如：一碗米饭，红烧肉，青菜...\"\n                {...register('input')}\n                disabled={isAnalyzing}\n                rows={4}\n              />\n              {errors.input && (\n                <p className=\"text-sm text-red-500\">{errors.input.message}</p>\n              )}\n            </div>\n          )}\n\n          {/* 图片输入 */}\n          {inputType === 'image' && (\n            <div className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"input\">补充说明（可选）</Label>\n                <Input\n                  id=\"input\"\n                  placeholder=\"可以补充说明食物的具体信息...\"\n                  {...register('input')}\n                  disabled={isAnalyzing}\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label>上传食物图片</Label>\n                <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\">\n                  {imagePreview ? (\n                    <div className=\"space-y-4\">\n                      <Image\n                        src={imagePreview}\n                        alt=\"预览\"\n                        width={400}\n                        height={300}\n                        className=\"max-w-full max-h-64 mx-auto rounded-lg object-contain\"\n                      />\n                      <Button\n                        type=\"button\"\n                        variant=\"outline\"\n                        onClick={() => {\n                          setSelectedImage(null);\n                          setImagePreview(null);\n                          if (fileInputRef.current) {\n                            fileInputRef.current.value = '';\n                          }\n                        }}\n                      >\n                        重新选择\n                      </Button>\n                    </div>\n                  ) : (\n                    <div className=\"space-y-4\">\n                      <Upload className=\"w-12 h-12 mx-auto text-gray-400\" />\n                      <div>\n                        <Button\n                          type=\"button\"\n                          variant=\"outline\"\n                          onClick={() => fileInputRef.current?.click()}\n                        >\n                          选择图片\n                        </Button>\n                        <p className=\"text-sm text-gray-500 mt-2\">\n                          支持 JPEG、PNG、WebP 格式，最大 10MB\n                        </p>\n                      </div>\n                    </div>\n                  )}\n                  <input\n                    ref={fileInputRef}\n                    type=\"file\"\n                    accept=\"image/jpeg,image/jpg,image/png,image/webp\"\n                    onChange={handleImageSelect}\n                    className=\"hidden\"\n                  />\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* 提交按钮 */}\n          <Button \n            type=\"submit\" \n            className=\"w-full\" \n            disabled={isAnalyzing || (inputType === 'image' && !selectedImage)}\n          >\n            {isAnalyzing ? (\n              <>\n                <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                分析中...\n              </>\n            ) : (\n              '开始分析'\n            )}\n          </Button>\n        </form>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAjBA;;;;;;;;;;;;;;;;;AAmBA,MAAM,kBAAkB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC/B,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC3B;AAIO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,eAAe,EAAE,cAAc,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAEpE,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACN,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAqB;QAC7B,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,SAAS;IACT,MAAM,oBAAoB,CAAC;QACzB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,SAAS;YACT,MAAM,eAAe;gBAAC;gBAAc;gBAAa;gBAAa;aAAa;YAC3E,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;gBACrC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,gBAAgB;YAChB,MAAM,UAAU,KAAK,OAAO;YAC5B,IAAI,KAAK,IAAI,GAAG,SAAS;gBACvB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,iBAAiB;YAEjB,OAAO;YACP,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,gBAAgB,EAAE,MAAM,EAAE;YAC5B;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,OAAO;IACP,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,MAAM;YACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,eAAe;QAEf,IAAI;YACF,IAAI;YAEJ,gBAAgB;YAChB,IAAI,cAAc,WAAW,eAAe;gBAC1C,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,QAAQ;gBAExB,MAAM,iBAAiB,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,qBAAqB,UAAU;oBACrE,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,KAAK,SAAS,CAAC,OAAO;wBACjD,gBAAgB;oBAClB;gBACF;gBAEA,IAAI,eAAe,IAAI,CAAC,OAAO,EAAE;oBAC/B,WAAW,eAAe,IAAI,CAAC,IAAI,CAAC,YAAY;gBAClD,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF;YAEA,SAAS;YACT,MAAM,mBAAmB,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,wBAAwB;gBAChE,OAAO,KAAK,KAAK;gBACjB;gBACA;YACF,GAAG;gBACD,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,KAAK,SAAS,CAAC,OAAO;oBACjD,gBAAgB;gBAClB;YACF;YAEA,IAAI,iBAAiB,IAAI,CAAC,OAAO,EAAE;gBACjC,eAAe;gBACf,gBAAgB,KAAK,KAAK,EAAE,WAAW;gBACvC,4HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,iBAAiB,CAAC,iBAAiB,IAAI,CAAC,IAAI;gBAEpE,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,OAAO;gBACP;gBACA,iBAAiB;gBACjB,gBAAgB;gBAChB,aAAa;YACf,OAAO;gBACL,MAAM,IAAI,MAAM,iBAAiB,IAAI,CAAC,KAAK,IAAI;YACjD;QAEF,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM,aAAa;YACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,QAAQ,EAAE,MAAM,SAAS;QAClD,SAAU;YACR,eAAe;QACjB;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,8OAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC;oBAAK,UAAU,aAAa;oBAAW,WAAU;;sCAEhD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS,cAAc,SAAS,YAAY;oCAC5C,SAAS,IAAM,aAAa;oCAC5B,WAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS,cAAc,UAAU,YAAY;oCAC7C,SAAS,IAAM,aAAa;oCAC5B,WAAU;;sDAEV,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;wBAMtC,cAAc,wBACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAQ;;;;;;8CACvB,8OAAC,oIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,aAAY;oCACX,GAAG,SAAS,QAAQ;oCACrB,UAAU;oCACV,MAAM;;;;;;gCAEP,OAAO,KAAK,kBACX,8OAAC;oCAAE,WAAU;8CAAwB,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;wBAM9D,cAAc,yBACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,aAAY;4CACX,GAAG,SAAS,QAAQ;4CACrB,UAAU;;;;;;;;;;;;8CAId,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC;4CAAI,WAAU;;gDACZ,6BACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,6HAAA,CAAA,UAAK;4DACJ,KAAK;4DACL,KAAI;4DACJ,OAAO;4DACP,QAAQ;4DACR,WAAU;;;;;;sEAEZ,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS;gEACP,iBAAiB;gEACjB,gBAAgB;gEAChB,IAAI,aAAa,OAAO,EAAE;oEACxB,aAAa,OAAO,CAAC,KAAK,GAAG;gEAC/B;4DACF;sEACD;;;;;;;;;;;yEAKH,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;;8EACC,8OAAC,kIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,SAAS,IAAM,aAAa,OAAO,EAAE;8EACtC;;;;;;8EAGD,8OAAC;oEAAE,WAAU;8EAA6B;;;;;;;;;;;;;;;;;;8DAMhD,8OAAC;oDACC,KAAK;oDACL,MAAK;oDACL,QAAO;oDACP,UAAU;oDACV,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAQpB,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU,eAAgB,cAAc,WAAW,CAAC;sCAEnD,4BACC;;kDACE,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;+CAInD;;;;;;;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 988, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 1211, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/diet/AnalysisResultForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useForm, useFieldArray } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { toast } from 'sonner';\nimport axios from 'axios';\n\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Trash2, Plus, Loader2 } from 'lucide-react';\nimport { useAuthStore } from '@/store/useAuthStore';\nimport { useDietStore } from '@/store/useDietStore';\n\nconst analysisResultSchema = z.object({\n  ingredients: z.array(z.object({\n    name: z.string().min(1, '食材名称不能为空'),\n    amount: z.number().min(0.1, '数量必须大于0'),\n    unit: z.string().min(1, '单位不能为空'),\n  })),\n  dishes: z.array(z.object({\n    name: z.string().min(1, '菜品名称不能为空'),\n    category: z.enum(['dish', 'staple', 'drink', 'side']),\n  })),\n  meal_type: z.enum(['breakfast', 'lunch', 'dinner', 'snack']),\n  record_time: z.string(),\n});\n\ntype AnalysisResultFormData = z.infer<typeof analysisResultSchema>;\n\nexport function AnalysisResultForm() {\n  const { user } = useAuthStore();\n  const { \n    analysisResult, \n    currentInput, \n    currentInputType, \n    currentImageUrl,\n    setIsSubmitting,\n    isSubmitting,\n    reset: resetDietStore\n  } = useDietStore();\n\n  const {\n    register,\n    control,\n    handleSubmit,\n    formState: { errors },\n    setValue,\n    watch,\n  } = useForm<AnalysisResultFormData>({\n    resolver: zodResolver(analysisResultSchema),\n  });\n\n  const { fields: ingredientFields, append: appendIngredient, remove: removeIngredient } = useFieldArray({\n    control,\n    name: 'ingredients',\n  });\n\n  const { fields: dishFields, append: appendDish, remove: removeDish } = useFieldArray({\n    control,\n    name: 'dishes',\n  });\n\n  // 初始化表单数据\n  useEffect(() => {\n    if (analysisResult) {\n      setValue('ingredients', analysisResult.ingredients);\n      setValue('dishes', analysisResult.dishes);\n      setValue('meal_type', analysisResult.meal_type);\n      setValue('record_time', analysisResult.estimated_time);\n    }\n  }, [analysisResult, setValue]);\n\n  // 提交确认的分析结果\n  const onSubmit = async (data: AnalysisResultFormData) => {\n    if (!user) {\n      toast.error('请先登录');\n      return;\n    }\n\n    setIsSubmitting(true);\n\n    try {\n      // 先分析热量\n      const calorieResponse = await axios.post('/api/ai/analyze-calories', {\n        ingredients: data.ingredients,\n      }, {\n        headers: {\n          'Authorization': `Bearer ${JSON.stringify(user)}`,\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (!calorieResponse.data.success) {\n        throw new Error('热量分析失败');\n      }\n\n      const calorieResult = calorieResponse.data.data;\n\n      // 创建饮食记录\n      const recordResponse = await axios.post('/api/diet/records', {\n        original_input: currentInput,\n        input_type: currentInputType,\n        image_url: currentImageUrl,\n        meal_type: data.meal_type,\n        record_time: data.record_time,\n        total_calories: calorieResult.total_calories,\n        ai_analysis: calorieResult.analysis,\n        dishes: data.dishes,\n        ingredients: data.ingredients.map((ing, index) => ({\n          ...ing,\n          calories_per_100g: calorieResult.breakdown[index]?.calories || 0,\n        })),\n      }, {\n        headers: {\n          'Authorization': `Bearer ${JSON.stringify(user)}`,\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (recordResponse.data.success) {\n        toast.success('饮食记录保存成功');\n        resetDietStore();\n      } else {\n        throw new Error(recordResponse.data.error || '保存失败');\n      }\n\n    } catch (error: unknown) {\n      console.error('保存饮食记录错误:', error);\n      const axiosError = error as { response?: { data?: { error?: string } } };\n      toast.error(axiosError.response?.data?.error || '保存失败，请稍后重试');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  if (!analysisResult) {\n    return null;\n  }\n\n  return (\n    <Card className=\"w-full max-w-4xl mx-auto\">\n      <CardHeader>\n        <CardTitle>确认分析结果</CardTitle>\n        <CardDescription>\n          请检查并修改AI分析的结果，确保信息准确\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n          {/* 餐次和时间 */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label>餐次</Label>\n              <Select\n                value={watch('meal_type')}\n                onValueChange={(value) => setValue('meal_type', value as 'breakfast' | 'lunch' | 'dinner' | 'snack')}\n              >\n                <SelectTrigger>\n                  <SelectValue placeholder=\"选择餐次\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"breakfast\">早餐</SelectItem>\n                  <SelectItem value=\"lunch\">午餐</SelectItem>\n                  <SelectItem value=\"dinner\">晚餐</SelectItem>\n                  <SelectItem value=\"snack\">加餐</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"record_time\">记录时间</Label>\n              <Input\n                id=\"record_time\"\n                type=\"datetime-local\"\n                {...register('record_time')}\n                disabled={isSubmitting}\n              />\n            </div>\n          </div>\n\n          {/* 食材列表 */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <Label className=\"text-lg font-semibold\">食材清单</Label>\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => appendIngredient({ name: '', amount: 0, unit: 'g' })}\n              >\n                <Plus className=\"w-4 h-4 mr-2\" />\n                添加食材\n              </Button>\n            </div>\n\n            {ingredientFields.map((field, index) => (\n              <div key={field.id} className=\"grid grid-cols-1 md:grid-cols-4 gap-4 p-4 border rounded-lg\">\n                <div className=\"space-y-2\">\n                  <Label>食材名称</Label>\n                  <Input\n                    {...register(`ingredients.${index}.name`)}\n                    placeholder=\"食材名称\"\n                    disabled={isSubmitting}\n                  />\n                  {errors.ingredients?.[index]?.name && (\n                    <p className=\"text-sm text-red-500\">{errors.ingredients[index]?.name?.message}</p>\n                  )}\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label>数量</Label>\n                  <Input\n                    type=\"number\"\n                    step=\"0.1\"\n                    {...register(`ingredients.${index}.amount`, { valueAsNumber: true })}\n                    placeholder=\"数量\"\n                    disabled={isSubmitting}\n                  />\n                  {errors.ingredients?.[index]?.amount && (\n                    <p className=\"text-sm text-red-500\">{errors.ingredients[index]?.amount?.message}</p>\n                  )}\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label>单位</Label>\n                  <Input\n                    {...register(`ingredients.${index}.unit`)}\n                    placeholder=\"单位\"\n                    disabled={isSubmitting}\n                  />\n                  {errors.ingredients?.[index]?.unit && (\n                    <p className=\"text-sm text-red-500\">{errors.ingredients[index]?.unit?.message}</p>\n                  )}\n                </div>\n\n                <div className=\"flex items-end\">\n                  <Button\n                    type=\"button\"\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => removeIngredient(index)}\n                    disabled={isSubmitting}\n                  >\n                    <Trash2 className=\"w-4 h-4\" />\n                  </Button>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* 菜品列表 */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <Label className=\"text-lg font-semibold\">菜品清单</Label>\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => appendDish({ name: '', category: 'dish' })}\n              >\n                <Plus className=\"w-4 h-4 mr-2\" />\n                添加菜品\n              </Button>\n            </div>\n\n            {dishFields.map((field, index) => (\n              <div key={field.id} className=\"grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border rounded-lg\">\n                <div className=\"space-y-2\">\n                  <Label>菜品名称</Label>\n                  <Input\n                    {...register(`dishes.${index}.name`)}\n                    placeholder=\"菜品名称\"\n                    disabled={isSubmitting}\n                  />\n                  {errors.dishes?.[index]?.name && (\n                    <p className=\"text-sm text-red-500\">{errors.dishes[index]?.name?.message}</p>\n                  )}\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label>分类</Label>\n                  <Select\n                    value={watch(`dishes.${index}.category`)}\n                    onValueChange={(value) => setValue(`dishes.${index}.category`, value as 'dish' | 'staple' | 'drink' | 'side')}\n                  >\n                    <SelectTrigger>\n                      <SelectValue placeholder=\"选择分类\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"dish\">菜品</SelectItem>\n                      <SelectItem value=\"staple\">主食</SelectItem>\n                      <SelectItem value=\"drink\">饮料</SelectItem>\n                      <SelectItem value=\"side\">配菜</SelectItem>\n                    </SelectContent>\n                  </Select>\n                </div>\n\n                <div className=\"flex items-end\">\n                  <Button\n                    type=\"button\"\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => removeDish(index)}\n                    disabled={isSubmitting}\n                  >\n                    <Trash2 className=\"w-4 h-4\" />\n                  </Button>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* 提交按钮 */}\n          <div className=\"flex gap-4\">\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={resetDietStore}\n              disabled={isSubmitting}\n              className=\"flex-1\"\n            >\n              取消\n            </Button>\n            <Button \n              type=\"submit\" \n              disabled={isSubmitting}\n              className=\"flex-1\"\n            >\n              {isSubmitting ? (\n                <>\n                  <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                  保存中...\n                </>\n              ) : (\n                '确认保存'\n              )}\n            </Button>\n          </div>\n        </form>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAhBA;;;;;;;;;;;;;;;;AAkBA,MAAM,uBAAuB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,aAAa,6KAAA,CAAA,IAAC,CAAC,KAAK,CAAC,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAC5B,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QACxB,QAAQ,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK;QAC5B,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B;IACA,QAAQ,6KAAA,CAAA,IAAC,CAAC,KAAK,CAAC,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACvB,MAAM,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QACxB,UAAU,6KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAQ;YAAU;YAAS;SAAO;IACtD;IACA,WAAW,6KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAa;QAAS;QAAU;KAAQ;IAC3D,aAAa,6KAAA,CAAA,IAAC,CAAC,MAAM;AACvB;AAIO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EACJ,cAAc,EACd,YAAY,EACZ,gBAAgB,EAChB,eAAe,EACf,eAAe,EACf,YAAY,EACZ,OAAO,cAAc,EACtB,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAEf,MAAM,EACJ,QAAQ,EACR,OAAO,EACP,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,QAAQ,EACR,KAAK,EACN,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAA0B;QAClC,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,EAAE,QAAQ,gBAAgB,EAAE,QAAQ,gBAAgB,EAAE,QAAQ,gBAAgB,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE;QACrG;QACA,MAAM;IACR;IAEA,MAAM,EAAE,QAAQ,UAAU,EAAE,QAAQ,UAAU,EAAE,QAAQ,UAAU,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE;QACnF;QACA,MAAM;IACR;IAEA,UAAU;IACV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB;YAClB,SAAS,eAAe,eAAe,WAAW;YAClD,SAAS,UAAU,eAAe,MAAM;YACxC,SAAS,aAAa,eAAe,SAAS;YAC9C,SAAS,eAAe,eAAe,cAAc;QACvD;IACF,GAAG;QAAC;QAAgB;KAAS;IAE7B,YAAY;IACZ,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,MAAM;YACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,QAAQ;YACR,MAAM,kBAAkB,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,4BAA4B;gBACnE,aAAa,KAAK,WAAW;YAC/B,GAAG;gBACD,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,KAAK,SAAS,CAAC,OAAO;oBACjD,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,gBAAgB,IAAI,CAAC,OAAO,EAAE;gBACjC,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,gBAAgB,gBAAgB,IAAI,CAAC,IAAI;YAE/C,SAAS;YACT,MAAM,iBAAiB,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,qBAAqB;gBAC3D,gBAAgB;gBAChB,YAAY;gBACZ,WAAW;gBACX,WAAW,KAAK,SAAS;gBACzB,aAAa,KAAK,WAAW;gBAC7B,gBAAgB,cAAc,cAAc;gBAC5C,aAAa,cAAc,QAAQ;gBACnC,QAAQ,KAAK,MAAM;gBACnB,aAAa,KAAK,WAAW,CAAC,GAAG,CAAC,CAAC,KAAK,QAAU,CAAC;wBACjD,GAAG,GAAG;wBACN,mBAAmB,cAAc,SAAS,CAAC,MAAM,EAAE,YAAY;oBACjE,CAAC;YACH,GAAG;gBACD,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,KAAK,SAAS,CAAC,OAAO;oBACjD,gBAAgB;gBAClB;YACF;YAEA,IAAI,eAAe,IAAI,CAAC,OAAO,EAAE;gBAC/B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd;YACF,OAAO;gBACL,MAAM,IAAI,MAAM,eAAe,IAAI,CAAC,KAAK,IAAI;YAC/C;QAEF,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM,aAAa;YACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,QAAQ,EAAE,MAAM,SAAS;QAClD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,IAAI,CAAC,gBAAgB;QACnB,OAAO;IACT;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,8OAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC;oBAAK,UAAU,aAAa;oBAAW,WAAU;;sCAEhD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC,kIAAA,CAAA,SAAM;4CACL,OAAO,MAAM;4CACb,eAAe,CAAC,QAAU,SAAS,aAAa;;8DAEhD,8OAAC,kIAAA,CAAA,gBAAa;8DACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAY;;;;;;sEAC9B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAQ;;;;;;sEAC1B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS;;;;;;sEAC3B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAQ;;;;;;;;;;;;;;;;;;;;;;;;8CAKhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAc;;;;;;sDAC7B,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACJ,GAAG,SAAS,cAAc;4CAC3B,UAAU;;;;;;;;;;;;;;;;;;sCAMhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAwB;;;;;;sDACzC,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,iBAAiB;oDAAE,MAAM;oDAAI,QAAQ;oDAAG,MAAM;gDAAI;;8DAEjE,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;gCAKpC,iBAAiB,GAAG,CAAC,CAAC,OAAO,sBAC5B,8OAAC;wCAAmB,WAAU;;0DAC5B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC,iIAAA,CAAA,QAAK;wDACH,GAAG,SAAS,CAAC,YAAY,EAAE,MAAM,KAAK,CAAC,CAAC;wDACzC,aAAY;wDACZ,UAAU;;;;;;oDAEX,OAAO,WAAW,EAAE,CAAC,MAAM,EAAE,sBAC5B,8OAAC;wDAAE,WAAU;kEAAwB,OAAO,WAAW,CAAC,MAAM,EAAE,MAAM;;;;;;;;;;;;0DAI1E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC,iIAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,MAAK;wDACJ,GAAG,SAAS,CAAC,YAAY,EAAE,MAAM,OAAO,CAAC,EAAE;4DAAE,eAAe;wDAAK,EAAE;wDACpE,aAAY;wDACZ,UAAU;;;;;;oDAEX,OAAO,WAAW,EAAE,CAAC,MAAM,EAAE,wBAC5B,8OAAC;wDAAE,WAAU;kEAAwB,OAAO,WAAW,CAAC,MAAM,EAAE,QAAQ;;;;;;;;;;;;0DAI5E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC,iIAAA,CAAA,QAAK;wDACH,GAAG,SAAS,CAAC,YAAY,EAAE,MAAM,KAAK,CAAC,CAAC;wDACzC,aAAY;wDACZ,UAAU;;;;;;oDAEX,OAAO,WAAW,EAAE,CAAC,MAAM,EAAE,sBAC5B,8OAAC;wDAAE,WAAU;kEAAwB,OAAO,WAAW,CAAC,MAAM,EAAE,MAAM;;;;;;;;;;;;0DAI1E,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,iBAAiB;oDAChC,UAAU;8DAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;uCA/Cd,MAAM,EAAE;;;;;;;;;;;sCAuDtB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAwB;;;;;;sDACzC,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,WAAW;oDAAE,MAAM;oDAAI,UAAU;gDAAO;;8DAEvD,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;gCAKpC,WAAW,GAAG,CAAC,CAAC,OAAO,sBACtB,8OAAC;wCAAmB,WAAU;;0DAC5B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC,iIAAA,CAAA,QAAK;wDACH,GAAG,SAAS,CAAC,OAAO,EAAE,MAAM,KAAK,CAAC,CAAC;wDACpC,aAAY;wDACZ,UAAU;;;;;;oDAEX,OAAO,MAAM,EAAE,CAAC,MAAM,EAAE,sBACvB,8OAAC;wDAAE,WAAU;kEAAwB,OAAO,MAAM,CAAC,MAAM,EAAE,MAAM;;;;;;;;;;;;0DAIrE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,8OAAC,kIAAA,CAAA,SAAM;wDACL,OAAO,MAAM,CAAC,OAAO,EAAE,MAAM,SAAS,CAAC;wDACvC,eAAe,CAAC,QAAU,SAAS,CAAC,OAAO,EAAE,MAAM,SAAS,CAAC,EAAE;;0EAE/D,8OAAC,kIAAA,CAAA,gBAAa;0EACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,8OAAC,kIAAA,CAAA,gBAAa;;kFACZ,8OAAC,kIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAO;;;;;;kFACzB,8OAAC,kIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAS;;;;;;kFAC3B,8OAAC,kIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAQ;;;;;;kFAC1B,8OAAC,kIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAO;;;;;;;;;;;;;;;;;;;;;;;;0DAK/B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,WAAW;oDAC1B,UAAU;8DAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;uCAvCd,MAAM,EAAE;;;;;;;;;;;sCA+CtB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;oCACV,WAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,6BACC;;0DACE,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA8B;;uDAInD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}, {"offset": {"line": 1945, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2117, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/diet/DietHistory.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { format } from 'date-fns';\nimport { zhCN } from 'date-fns/locale';\nimport axios from 'axios';\nimport { toast } from 'sonner';\nimport Image from 'next/image';\n\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader } from '@/components/ui/card';\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';\nimport { Trash2, Clock, Utensils, Loader2, RefreshCw } from 'lucide-react';\nimport { useAuthStore } from '@/store/useAuthStore';\nimport { DietRecord } from '@/types';\n\nexport function DietHistory() {\n  const [records, setRecords] = useState<DietRecord[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [recordToDelete, setRecordToDelete] = useState<DietRecord | null>(null);\n  const [deleting, setDeleting] = useState(false);\n  \n  const { user } = useAuthStore();\n\n  // 获取饮食记录\n  const fetchRecords = async () => {\n    if (!user) return;\n\n    try {\n      setLoading(true);\n      const response = await axios.get('/api/diet/records', {\n        headers: {\n          'Authorization': `Bearer ${JSON.stringify(user)}`,\n        },\n      });\n\n      if (response.data.success) {\n        setRecords(response.data.data);\n      } else {\n        toast.error('获取历史记录失败');\n      }\n    } catch (error: unknown) {\n      console.error('获取历史记录错误:', error);\n      const axiosError = error as { response?: { data?: { error?: string } } };\n      toast.error(axiosError.response?.data?.error || '获取历史记录失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 删除记录\n  const handleDelete = async () => {\n    if (!recordToDelete || !user) return;\n\n    try {\n      setDeleting(true);\n      const response = await axios.delete(`/api/diet/records/${recordToDelete.id}`, {\n        headers: {\n          'Authorization': `Bearer ${JSON.stringify(user)}`,\n        },\n      });\n\n      if (response.data.success) {\n        toast.success('记录删除成功');\n        setRecords(records.filter(record => record.id !== recordToDelete.id));\n        setDeleteDialogOpen(false);\n        setRecordToDelete(null);\n      } else {\n        toast.error('删除失败');\n      }\n    } catch (error: unknown) {\n      console.error('删除记录错误:', error);\n      const axiosError = error as { response?: { data?: { error?: string } } };\n      toast.error(axiosError.response?.data?.error || '删除失败');\n    } finally {\n      setDeleting(false);\n    }\n  };\n\n  // 获取餐次显示名称\n  const getMealTypeName = (mealType: string) => {\n    const names = {\n      breakfast: '早餐',\n      lunch: '午餐',\n      dinner: '晚餐',\n      snack: '加餐',\n    };\n    return names[mealType as keyof typeof names] || mealType;\n  };\n\n\n\n  useEffect(() => {\n    fetchRecords();\n  }, [user]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  if (loading) {\n    return (\n      <Card>\n        <CardContent className=\"flex items-center justify-center py-8\">\n          <Loader2 className=\"w-6 h-6 animate-spin mr-2\" />\n          加载中...\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h2 className=\"text-2xl font-bold\">饮食历史</h2>\n          <p className=\"text-gray-600\">查看您的饮食记录历史</p>\n        </div>\n        <Button\n          variant=\"outline\"\n          onClick={fetchRecords}\n          disabled={loading}\n        >\n          <RefreshCw className=\"w-4 h-4 mr-2\" />\n          刷新\n        </Button>\n      </div>\n\n      {records.length === 0 ? (\n        <Card>\n          <CardContent className=\"text-center py-8\">\n            <Utensils className=\"w-12 h-12 mx-auto text-gray-400 mb-4\" />\n            <p className=\"text-gray-500\">还没有饮食记录</p>\n            <p className=\"text-sm text-gray-400 mt-2\">开始记录您的第一餐吧！</p>\n          </CardContent>\n        </Card>\n      ) : (\n        <div className=\"space-y-4\">\n          {records.map((record) => (\n            <Card key={record.id} className=\"hover:shadow-md transition-shadow\">\n              <CardHeader className=\"pb-3\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-2\">\n                    <Clock className=\"w-4 h-4 text-gray-500\" />\n                    <span className=\"text-sm text-gray-600\">\n                      {format(new Date(record.record_time), 'yyyy年MM月dd日 HH:mm', { locale: zhCN })}\n                    </span>\n                    <span className=\"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\">\n                      {getMealTypeName(record.meal_type)}\n                    </span>\n                  </div>\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={() => {\n                      setRecordToDelete(record);\n                      setDeleteDialogOpen(true);\n                    }}\n                  >\n                    <Trash2 className=\"w-4 h-4 text-red-500\" />\n                  </Button>\n                </div>\n              </CardHeader>\n              \n              <CardContent className=\"space-y-4\">\n                {/* 原始输入 */}\n                <div>\n                  <h4 className=\"font-medium text-sm text-gray-700 mb-2\">原始输入</h4>\n                  <div className=\"flex flex-col sm:flex-row sm:items-start space-y-2 sm:space-y-0 sm:space-x-3\">\n                    {record.input_type === 'image' && record.image_url && (\n                      <Image\n                        src={record.image_url}\n                        alt=\"食物图片\"\n                        width={80}\n                        height={80}\n                        className=\"w-16 h-16 sm:w-20 sm:h-20 rounded-lg object-cover mx-auto sm:mx-0\"\n                      />\n                    )}\n                    <p className=\"text-sm text-gray-600 flex-1 text-center sm:text-left\">{record.original_input}</p>\n                  </div>\n                </div>\n\n                {/* 食材清单 */}\n                {record.ingredients && record.ingredients.length > 0 && (\n                  <div>\n                    <h4 className=\"font-medium text-sm text-gray-700 mb-2\">食材清单</h4>\n                    <div className=\"flex flex-wrap gap-2\">\n                      {record.ingredients.map((ingredient, index) => (\n                        <span\n                          key={index}\n                          className=\"px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full\"\n                        >\n                          {ingredient.name} {ingredient.amount}{ingredient.unit}\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n                )}\n\n                {/* 菜品清单 */}\n                {record.dishes && record.dishes.length > 0 && (\n                  <div>\n                    <h4 className=\"font-medium text-sm text-gray-700 mb-2\">菜品清单</h4>\n                    <div className=\"flex flex-wrap gap-2\">\n                      {record.dishes.map((dish, index) => (\n                        <span\n                          key={index}\n                          className=\"px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full\"\n                        >\n                          {dish}\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n                )}\n\n                {/* 热量和分析 */}\n                <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between pt-2 border-t space-y-2 sm:space-y-0\">\n                  <div className=\"flex items-center justify-center sm:justify-start space-x-4\">\n                    <span className=\"text-lg font-semibold text-red-600\">\n                      {record.total_calories} 卡路里\n                    </span>\n                  </div>\n                  {record.ai_analysis && (\n                    <p className=\"text-sm text-gray-600 max-w-md text-center sm:text-right\">\n                      {record.ai_analysis}\n                    </p>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      )}\n\n      {/* 删除确认对话框 */}\n      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>\n        <DialogContent>\n          <DialogHeader>\n            <DialogTitle>确认删除</DialogTitle>\n            <DialogDescription>\n              您确定要删除这条饮食记录吗？此操作无法撤销。\n            </DialogDescription>\n          </DialogHeader>\n          <DialogFooter>\n            <Button\n              variant=\"outline\"\n              onClick={() => setDeleteDialogOpen(false)}\n              disabled={deleting}\n            >\n              取消\n            </Button>\n            <Button\n              variant=\"destructive\"\n              onClick={handleDelete}\n              disabled={deleting}\n            >\n              {deleting ? (\n                <>\n                  <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                  删除中...\n                </>\n              ) : (\n                '确认删除'\n              )}\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAbA;;;;;;;;;;;;;AAgBO,SAAS;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IACxE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAE5B,SAAS;IACT,MAAM,eAAe;QACnB,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,qBAAqB;gBACpD,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,KAAK,SAAS,CAAC,OAAO;gBACnD;YACF;YAEA,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACzB,WAAW,SAAS,IAAI,CAAC,IAAI;YAC/B,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM,aAAa;YACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,QAAQ,EAAE,MAAM,SAAS;QAClD,SAAU;YACR,WAAW;QACb;IACF;IAEA,OAAO;IACP,MAAM,eAAe;QACnB,IAAI,CAAC,kBAAkB,CAAC,MAAM;QAE9B,IAAI;YACF,YAAY;YACZ,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,kBAAkB,EAAE,eAAe,EAAE,EAAE,EAAE;gBAC5E,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,KAAK,SAAS,CAAC,OAAO;gBACnD;YACF;YAEA,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACzB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,WAAW,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK,eAAe,EAAE;gBACnE,oBAAoB;gBACpB,kBAAkB;YACpB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM,aAAa;YACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,QAAQ,EAAE,MAAM,SAAS;QAClD,SAAU;YACR,YAAY;QACd;IACF;IAEA,WAAW;IACX,MAAM,kBAAkB,CAAC;QACvB,MAAM,QAAQ;YACZ,WAAW;YACX,OAAO;YACP,QAAQ;YACR,OAAO;QACT;QACA,OAAO,KAAK,CAAC,SAA+B,IAAI;IAClD;IAIA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAK,GAAG,kDAAkD;IAE9D,IAAI,SAAS;QACX,qBACE,8OAAC,gIAAA,CAAA,OAAI;sBACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBAA8B;;;;;;;;;;;;IAKzD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAE/B,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,SAAS;wBACT,UAAU;;0CAEV,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;YAKzC,QAAQ,MAAM,KAAK,kBAClB,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAC7B,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;qCAI9C,8OAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,gIAAA,CAAA,OAAI;wBAAiB,WAAU;;0CAC9B,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDAAK,WAAU;8DACb,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,WAAW,GAAG,qBAAqB;wDAAE,QAAQ,iJAAA,CAAA,OAAI;oDAAC;;;;;;8DAE5E,8OAAC;oDAAK,WAAU;8DACb,gBAAgB,OAAO,SAAS;;;;;;;;;;;;sDAGrC,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;gDACP,kBAAkB;gDAClB,oBAAoB;4CACtB;sDAEA,cAAA,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAKxB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDAErB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;;oDACZ,OAAO,UAAU,KAAK,WAAW,OAAO,SAAS,kBAChD,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAK,OAAO,SAAS;wDACrB,KAAI;wDACJ,OAAO;wDACP,QAAQ;wDACR,WAAU;;;;;;kEAGd,8OAAC;wDAAE,WAAU;kEAAyD,OAAO,cAAc;;;;;;;;;;;;;;;;;;oCAK9F,OAAO,WAAW,IAAI,OAAO,WAAW,CAAC,MAAM,GAAG,mBACjD,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;0DACZ,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY,sBACnC,8OAAC;wDAEC,WAAU;;4DAET,WAAW,IAAI;4DAAC;4DAAE,WAAW,MAAM;4DAAE,WAAW,IAAI;;uDAHhD;;;;;;;;;;;;;;;;oCAWd,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG,mBACvC,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;0DACZ,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,sBACxB,8OAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;;;;;;;kDAWf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;;wDACb,OAAO,cAAc;wDAAC;;;;;;;;;;;;4CAG1B,OAAO,WAAW,kBACjB,8OAAC;gDAAE,WAAU;0DACV,OAAO,WAAW;;;;;;;;;;;;;;;;;;;uBAtFlB,OAAO,EAAE;;;;;;;;;;0BAiG1B,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;;sCACZ,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAIrB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,oBAAoB;oCACnC,UAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;8CAET,yBACC;;0DACE,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA8B;;uDAInD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}, {"offset": {"line": 2665, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/settings/UserSettings.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { toast } from 'sonner';\nimport axios from 'axios';\n\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Loader2, RefreshCw, TestTube } from 'lucide-react';\nimport { useAuthStore } from '@/store/useAuthStore';\n\n\nconst settingsSchema = z.object({\n  ai_model: z.string().min(1, 'AI模型不能为空'),\n  api_base_url: z.string().url('API地址格式不正确'),\n  api_key: z.string().min(1, 'API密钥不能为空'),\n});\n\ntype SettingsFormData = z.infer<typeof settingsSchema>;\n\nexport function UserSettings() {\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [testingConnection, setTestingConnection] = useState(false);\n  const [availableModels, setAvailableModels] = useState<string[]>([]);\n  const [loadingModels, setLoadingModels] = useState(false);\n  \n  const { user } = useAuthStore();\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    setValue,\n    watch,\n    reset,\n  } = useForm<SettingsFormData>({\n    resolver: zodResolver(settingsSchema),\n    defaultValues: {\n      ai_model: 'gpt-4o',\n      api_base_url: 'https://api.openai.com',\n      api_key: '',\n    },\n  });\n\n  // 获取用户设置\n  const fetchSettings = async () => {\n    if (!user) return;\n\n    try {\n      setLoading(true);\n      const response = await axios.get('/api/user/settings', {\n        headers: {\n          'Authorization': `Bearer ${JSON.stringify(user)}`,\n        },\n      });\n\n      if (response.data.success && response.data.data) {\n        const settings = response.data.data;\n        reset({\n          ai_model: settings.ai_model,\n          api_base_url: settings.api_base_url,\n          api_key: settings.api_key,\n        });\n      }\n    } catch (error: unknown) {\n      console.error('获取设置错误:', error);\n      // 如果没有设置，使用默认值，不显示错误\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取可用模型\n  const fetchAvailableModels = async () => {\n    if (!user) return;\n\n    try {\n      setLoadingModels(true);\n\n      const response = await axios.get('/api/ai/models', {\n        headers: {\n          'Authorization': `Bearer ${JSON.stringify(user)}`,\n        },\n      });\n\n      if (response.data.success) {\n        setAvailableModels(response.data.data);\n        toast.success(`成功获取 ${response.data.data.length} 个模型`);\n      } else {\n        toast.error(response.data.error || '获取模型列表失败');\n      }\n    } catch (error: unknown) {\n      console.error('获取模型列表错误:', error);\n      const axiosError = error as { response?: { data?: { error?: string } } };\n      toast.error(axiosError.response?.data?.error || '获取模型列表失败，请检查API配置');\n    } finally {\n      setLoadingModels(false);\n    }\n  };\n\n  // 测试连接\n  const testConnection = async () => {\n    if (!user) return;\n\n    const formData = watch();\n    \n    try {\n      setTestingConnection(true);\n      \n      // 先保存设置\n      await axios.put('/api/user/settings', formData, {\n        headers: {\n          'Authorization': `Bearer ${JSON.stringify(user)}`,\n          'Content-Type': 'application/json',\n        },\n      });\n\n      // 然后测试连接\n      const response = await axios.get('/api/ai/models', {\n        headers: {\n          'Authorization': `Bearer ${JSON.stringify(user)}`,\n        },\n      });\n\n      if (response.data.success) {\n        toast.success('连接测试成功');\n        setAvailableModels(response.data.data);\n      } else {\n        toast.error('连接测试失败');\n      }\n    } catch (error: unknown) {\n      console.error('测试连接错误:', error);\n      const axiosError = error as { response?: { data?: { error?: string } } };\n      toast.error(axiosError.response?.data?.error || '连接测试失败');\n    } finally {\n      setTestingConnection(false);\n    }\n  };\n\n  // 保存设置\n  const onSubmit = async (data: SettingsFormData) => {\n    if (!user) return;\n\n    try {\n      setSaving(true);\n      const response = await axios.put('/api/user/settings', data, {\n        headers: {\n          'Authorization': `Bearer ${JSON.stringify(user)}`,\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (response.data.success) {\n        toast.success('设置保存成功');\n      } else {\n        toast.error(response.data.error || '保存失败');\n      }\n    } catch (error: unknown) {\n      console.error('保存设置错误:', error);\n      const axiosError = error as { response?: { data?: { error?: string } } };\n      toast.error(axiosError.response?.data?.error || '保存失败');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchSettings();\n  }, [user]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  if (loading) {\n    return (\n      <Card>\n        <CardContent className=\"flex items-center justify-center py-8\">\n          <Loader2 className=\"w-6 h-6 animate-spin mr-2\" />\n          加载中...\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h2 className=\"text-2xl font-bold\">设置</h2>\n        <p className=\"text-gray-600\">配置您的AI模型和API设置</p>\n      </div>\n\n      <Card>\n        <CardHeader>\n          <CardTitle>AI模型配置</CardTitle>\n          <CardDescription>\n            配置用于饮食分析的AI模型和API设置\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n            {/* API基础URL */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"api_base_url\">API基础URL</Label>\n              <Input\n                id=\"api_base_url\"\n                type=\"url\"\n                placeholder=\"https://api.openai.com\"\n                {...register('api_base_url')}\n                disabled={saving}\n              />\n              {errors.api_base_url && (\n                <p className=\"text-sm text-red-500\">{errors.api_base_url.message}</p>\n              )}\n            </div>\n\n            {/* API密钥 */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"api_key\">API密钥</Label>\n              <Input\n                id=\"api_key\"\n                type=\"password\"\n                placeholder=\"输入您的API密钥\"\n                {...register('api_key')}\n                disabled={saving}\n              />\n              {errors.api_key && (\n                <p className=\"text-sm text-red-500\">{errors.api_key.message}</p>\n              )}\n            </div>\n\n            {/* 测试连接按钮 */}\n            <div className=\"flex gap-2\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={testConnection}\n                disabled={testingConnection || saving}\n              >\n                {testingConnection ? (\n                  <>\n                    <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                    测试中...\n                  </>\n                ) : (\n                  <>\n                    <TestTube className=\"w-4 h-4 mr-2\" />\n                    测试连接\n                  </>\n                )}\n              </Button>\n\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={fetchAvailableModels}\n                disabled={loadingModels || saving}\n              >\n                {loadingModels ? (\n                  <>\n                    <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                    获取中...\n                  </>\n                ) : (\n                  <>\n                    <RefreshCw className=\"w-4 h-4 mr-2\" />\n                    获取模型列表\n                  </>\n                )}\n              </Button>\n            </div>\n\n            {/* AI模型选择 */}\n            <div className=\"space-y-2\">\n              <Label>AI模型</Label>\n              <Select\n                value={watch('ai_model')}\n                onValueChange={(value) => setValue('ai_model', value)}\n              >\n                <SelectTrigger>\n                  <SelectValue placeholder=\"选择AI模型\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {availableModels.length > 0 ? (\n                    availableModels.map((model) => (\n                      <SelectItem key={model} value={model}>\n                        {model}\n                      </SelectItem>\n                    ))\n                  ) : (\n                    <SelectItem value=\"no-models\" disabled>\n                      请先测试连接获取模型列表\n                    </SelectItem>\n                  )}\n                </SelectContent>\n              </Select>\n              {errors.ai_model && (\n                <p className=\"text-sm text-red-500\">{errors.ai_model.message}</p>\n              )}\n            </div>\n\n            {/* 保存按钮 */}\n            <Button type=\"submit\" disabled={saving} className=\"w-full\">\n              {saving ? (\n                <>\n                  <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                  保存中...\n                </>\n              ) : (\n                '保存设置'\n              )}\n            </Button>\n          </form>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAfA;;;;;;;;;;;;;;;AAkBA,MAAM,iBAAiB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9B,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,cAAc,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IAC7B,SAAS,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC7B;AAIO,SAAS;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,QAAQ,EACR,KAAK,EACL,KAAK,EACN,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAoB;QAC5B,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,UAAU;YACV,cAAc;YACd,SAAS;QACX;IACF;IAEA,SAAS;IACT,MAAM,gBAAgB;QACpB,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,sBAAsB;gBACrD,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,KAAK,SAAS,CAAC,OAAO;gBACnD;YACF;YAEA,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBAC/C,MAAM,WAAW,SAAS,IAAI,CAAC,IAAI;gBACnC,MAAM;oBACJ,UAAU,SAAS,QAAQ;oBAC3B,cAAc,SAAS,YAAY;oBACnC,SAAS,SAAS,OAAO;gBAC3B;YACF;QACF,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,WAAW;QACzB,qBAAqB;QACvB,SAAU;YACR,WAAW;QACb;IACF;IAEA,SAAS;IACT,MAAM,uBAAuB;QAC3B,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,iBAAiB;YAEjB,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,kBAAkB;gBACjD,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,KAAK,SAAS,CAAC,OAAO;gBACnD;YACF;YAEA,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACzB,mBAAmB,SAAS,IAAI,CAAC,IAAI;gBACrC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACvD,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,KAAK,IAAI;YACrC;QACF,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM,aAAa;YACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,QAAQ,EAAE,MAAM,SAAS;QAClD,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,OAAO;IACP,MAAM,iBAAiB;QACrB,IAAI,CAAC,MAAM;QAEX,MAAM,WAAW;QAEjB,IAAI;YACF,qBAAqB;YAErB,QAAQ;YACR,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,sBAAsB,UAAU;gBAC9C,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,KAAK,SAAS,CAAC,OAAO;oBACjD,gBAAgB;gBAClB;YACF;YAEA,SAAS;YACT,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,kBAAkB;gBACjD,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,KAAK,SAAS,CAAC,OAAO;gBACnD;YACF;YAEA,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACzB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,mBAAmB,SAAS,IAAI,CAAC,IAAI;YACvC,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM,aAAa;YACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,QAAQ,EAAE,MAAM,SAAS;QAClD,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,OAAO;IACP,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,UAAU;YACV,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,sBAAsB,MAAM;gBAC3D,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,KAAK,SAAS,CAAC,OAAO;oBACjD,gBAAgB;gBAClB;YACF;YAEA,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACzB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,KAAK,IAAI;YACrC;QACF,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM,aAAa;YACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,QAAQ,EAAE,MAAM,SAAS;QAClD,SAAU;YACR,UAAU;QACZ;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAK,GAAG,kDAAkD;IAE9D,IAAI,SAAS;QACX,qBACE,8OAAC,gIAAA,CAAA,OAAI;sBACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBAA8B;;;;;;;;;;;;IAKzD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAG/B,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAK,UAAU,aAAa;4BAAW,WAAU;;8CAEhD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAe;;;;;;sDAC9B,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,aAAY;4CACX,GAAG,SAAS,eAAe;4CAC5B,UAAU;;;;;;wCAEX,OAAO,YAAY,kBAClB,8OAAC;4CAAE,WAAU;sDAAwB,OAAO,YAAY,CAAC,OAAO;;;;;;;;;;;;8CAKpE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;sDACzB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,aAAY;4CACX,GAAG,SAAS,UAAU;4CACvB,UAAU;;;;;;wCAEX,OAAO,OAAO,kBACb,8OAAC;4CAAE,WAAU;sDAAwB,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;8CAK/D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,SAAS;4CACT,UAAU,qBAAqB;sDAE9B,kCACC;;kEACE,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;6EAInD;;kEACE,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;sDAM3C,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,SAAS;4CACT,UAAU,iBAAiB;sDAE1B,8BACC;;kEACE,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;6EAInD;;kEACE,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;8CAQ9C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC,kIAAA,CAAA,SAAM;4CACL,OAAO,MAAM;4CACb,eAAe,CAAC,QAAU,SAAS,YAAY;;8DAE/C,8OAAC,kIAAA,CAAA,gBAAa;8DACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,kIAAA,CAAA,gBAAa;8DACX,gBAAgB,MAAM,GAAG,IACxB,gBAAgB,GAAG,CAAC,CAAC,sBACnB,8OAAC,kIAAA,CAAA,aAAU;4DAAa,OAAO;sEAC5B;2DADc;;;;kFAKnB,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;wDAAY,QAAQ;kEAAC;;;;;;;;;;;;;;;;;wCAM5C,OAAO,QAAQ,kBACd,8OAAC;4CAAE,WAAU;sDAAwB,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;8CAKhE,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,UAAU;oCAAQ,WAAU;8CAC/C,uBACC;;0DACE,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA8B;;uDAInD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}, {"offset": {"line": 3176, "column": 0}, "map": {"version": 3, "sources": ["file:///root/test/l-weight/app/src/components/dashboard/DashboardContent.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { LogOut, Settings, History, Plus } from 'lucide-react';\nimport { useAuthStore } from '@/store/useAuthStore';\nimport { useDietStore } from '@/store/useDietStore';\nimport { DietInputForm } from '@/components/diet/DietInputForm';\nimport { AnalysisResultForm } from '@/components/diet/AnalysisResultForm';\nimport { DietHistory } from '@/components/diet/DietHistory';\nimport { UserSettings } from '@/components/settings/UserSettings';\n\nexport function DashboardContent() {\n  const [activeTab, setActiveTab] = useState<'input' | 'history' | 'settings'>('input');\n  const router = useRouter();\n  const { user, logout } = useAuthStore();\n  const { analysisResult } = useDietStore();\n\n  const handleLogout = () => {\n    logout();\n    router.push('/login');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* 顶部导航 */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-xl font-semibold text-gray-900\">\n                饮食热量分析系统\n              </h1>\n            </div>\n            \n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-gray-600\">\n                欢迎，{user?.username}\n              </span>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={handleLogout}\n              >\n                <LogOut className=\"w-4 h-4 mr-2\" />\n                退出登录\n              </Button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* 主要内容区域 */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"flex flex-col lg:flex-row gap-8\">\n          {/* 侧边栏导航 */}\n          <aside className=\"w-full lg:w-64 space-y-2\">\n            <Card>\n              <CardContent className=\"p-4\">\n                <nav className=\"space-y-2\">\n                  <Button\n                    variant={activeTab === 'input' ? 'default' : 'ghost'}\n                    className=\"w-full justify-start\"\n                    onClick={() => setActiveTab('input')}\n                  >\n                    <Plus className=\"w-4 h-4 mr-2\" />\n                    饮食记录\n                  </Button>\n                  \n                  <Button\n                    variant={activeTab === 'history' ? 'default' : 'ghost'}\n                    className=\"w-full justify-start\"\n                    onClick={() => setActiveTab('history')}\n                  >\n                    <History className=\"w-4 h-4 mr-2\" />\n                    历史记录\n                  </Button>\n                  \n                  <Button\n                    variant={activeTab === 'settings' ? 'default' : 'ghost'}\n                    className=\"w-full justify-start\"\n                    onClick={() => setActiveTab('settings')}\n                  >\n                    <Settings className=\"w-4 h-4 mr-2\" />\n                    设置\n                  </Button>\n                </nav>\n              </CardContent>\n            </Card>\n\n            {/* 管理员入口 */}\n            {user?.role === 'admin' && (\n              <Card>\n                <CardHeader className=\"pb-3\">\n                  <CardTitle className=\"text-sm\">管理员功能</CardTitle>\n                </CardHeader>\n                <CardContent className=\"pt-0\">\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    className=\"w-full\"\n                    onClick={() => router.push('/admin')}\n                  >\n                    用户管理\n                  </Button>\n                </CardContent>\n              </Card>\n            )}\n          </aside>\n\n          {/* 主要内容 */}\n          <div className=\"flex-1\">\n            {activeTab === 'input' && (\n              <div className=\"space-y-8\">\n                {!analysisResult ? (\n                  <DietInputForm />\n                ) : (\n                  <AnalysisResultForm />\n                )}\n              </div>\n            )}\n\n            {activeTab === 'history' && (\n              <DietHistory />\n            )}\n\n            {activeTab === 'settings' && (\n              <UserSettings />\n            )}\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AAcO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoC;IAC7E,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IACpC,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAEtC,MAAM,eAAe;QACnB;QACA,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;;;;;;0CAKtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAAwB;4CAClC,MAAM;;;;;;;kDAEZ,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;;0DAET,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7C,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAM,WAAU;;8CACf,8OAAC,gIAAA,CAAA,OAAI;8CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS,cAAc,UAAU,YAAY;oDAC7C,WAAU;oDACV,SAAS,IAAM,aAAa;;sEAE5B,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAInC,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS,cAAc,YAAY,YAAY;oDAC/C,WAAU;oDACV,SAAS,IAAM,aAAa;;sEAE5B,8OAAC,wMAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAItC,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS,cAAc,aAAa,YAAY;oDAChD,WAAU;oDACV,SAAS,IAAM,aAAa;;sEAE5B,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;gCAQ5C,MAAM,SAAS,yBACd,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAU;;;;;;;;;;;sDAEjC,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,OAAO,IAAI,CAAC;0DAC5B;;;;;;;;;;;;;;;;;;;;;;;sCAST,8OAAC;4BAAI,WAAU;;gCACZ,cAAc,yBACb,8OAAC;oCAAI,WAAU;8CACZ,CAAC,+BACA,8OAAC,2IAAA,CAAA,gBAAa;;;;6DAEd,8OAAC,gJAAA,CAAA,qBAAkB;;;;;;;;;;gCAKxB,cAAc,2BACb,8OAAC,yIAAA,CAAA,cAAW;;;;;gCAGb,cAAc,4BACb,8OAAC,8IAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B", "debugId": null}}]}