(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[454],{285:(e,s,r)=>{"use strict";r.d(s,{$:()=>o});var t=r(5155);r(2115);var a=r(9708),i=r(2085),n=r(9434);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:s,variant:r,size:i,asChild:o=!1,...l}=e,c=o?a.DX:"button";return(0,t.jsx)(c,{"data-slot":"button",className:(0,n.cn)(d({variant:r,size:i,className:s})),...l})}},1001:(e,s,r)=>{"use strict";r.d(s,{n:()=>i});var t=r(5453),a=r(6786);let i=(0,t.v)()((0,a.Zr)((e,s)=>({user:null,isAuthenticated:!1,isLoading:!1,login:s=>{e({user:s,isAuthenticated:!0,isLoading:!1})},logout:()=>{e({user:null,isAuthenticated:!1,isLoading:!1})},setLoading:s=>{e({isLoading:s})},updateUser:r=>{let t=s().user;t&&e({user:{...t,...r}})}}),{name:"auth-storage",partialize:e=>({user:e.user,isAuthenticated:e.isAuthenticated})}))},1470:(e,s,r)=>{"use strict";r.d(s,{AuthGuard:()=>d});var t=r(5155),a=r(2115),i=r(5695),n=r(1001);function d(e){let{children:s,requireAuth:r=!0,requireAdmin:d=!1,redirectTo:o}=e,{isAuthenticated:l,user:c,isLoading:u}=(0,n.n)(),m=(0,i.useRouter)();return((0,a.useEffect)(()=>{if(!u){if(r&&!l)return void m.push(o||"/login");if(d&&(null==c?void 0:c.role)!=="admin"||!r&&l)return void m.push("/dashboard")}},[l,c,u,r,d,m,o]),u)?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"})}):r&&!l||d&&(null==c?void 0:c.role)!=="admin"||!r&&l?null:(0,t.jsx)(t.Fragment,{children:s})}},2523:(e,s,r)=>{"use strict";r.d(s,{p:()=>i});var t=r(5155);r(2115);var a=r(9434);function i(e){let{className:s,type:r,...i}=e;return(0,t.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...i})}},2972:(e,s,r)=>{"use strict";r.d(s,{RegisterForm:()=>g});var t=r(5155),a=r(2115),i=r(5695),n=r(2177),d=r(221),o=r(8309),l=r(3464),c=r(6671),u=r(285),m=r(2523),p=r(5057),x=r(6695),h=r(1001);let v=o.Ik({username:o.Yj().min(3,"用户名至少3个字符").max(50,"用户名最多50个字符"),email:o.Yj().email("邮箱格式不正确"),password:o.Yj().min(6,"密码至少6个字符"),confirmPassword:o.Yj()}).refine(e=>e.password===e.confirmPassword,{message:"两次输入的密码不一致",path:["confirmPassword"]});function g(){let[e,s]=(0,a.useState)(!1),r=(0,i.useRouter)(),{login:o}=(0,h.n)(),{register:g,handleSubmit:f,formState:{errors:b}}=(0,n.mN)({resolver:(0,d.u)(v)}),j=async e=>{s(!0);try{let s=await l.A.post("/api/auth/register",e);s.data.success?(o(s.data.data),c.oR.success("注册成功"),r.push("/dashboard")):c.oR.error(s.data.error||"注册失败")}catch(e){var t,a;console.error("注册错误:",e),c.oR.error((null==(a=e.response)||null==(t=a.data)?void 0:t.error)||"注册失败，请稍后重试")}finally{s(!1)}};return(0,t.jsxs)(x.Zp,{className:"w-full max-w-md mx-auto",children:[(0,t.jsxs)(x.aR,{children:[(0,t.jsx)(x.ZB,{children:"注册"}),(0,t.jsx)(x.BT,{children:"创建一个新账号来开始使用饮食热量分析系统"})]}),(0,t.jsxs)(x.Wu,{children:[(0,t.jsxs)("form",{onSubmit:f(j),className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"username",children:"用户名"}),(0,t.jsx)(m.p,{id:"username",type:"text",placeholder:"请输入用户名",...g("username"),disabled:e}),b.username&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:b.username.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"email",children:"邮箱"}),(0,t.jsx)(m.p,{id:"email",type:"email",placeholder:"请输入邮箱",...g("email"),disabled:e}),b.email&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:b.email.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"password",children:"密码"}),(0,t.jsx)(m.p,{id:"password",type:"password",placeholder:"请输入密码",...g("password"),disabled:e}),b.password&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:b.password.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"confirmPassword",children:"确认密码"}),(0,t.jsx)(m.p,{id:"confirmPassword",type:"password",placeholder:"请再次输入密码",...g("confirmPassword"),disabled:e}),b.confirmPassword&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:b.confirmPassword.message})]}),(0,t.jsx)(u.$,{type:"submit",className:"w-full",disabled:e,children:e?"注册中...":"注册"})]}),(0,t.jsx)("div",{className:"mt-4 text-center",children:(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["已有账号？"," ",(0,t.jsx)("button",{type:"button",className:"text-blue-600 hover:underline",onClick:()=>r.push("/login"),children:"立即登录"})]})})]})]})}},5057:(e,s,r)=>{"use strict";r.d(s,{J:()=>n});var t=r(5155);r(2115);var a=r(968),i=r(9434);function n(e){let{className:s,...r}=e;return(0,t.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...r})}},6695:(e,s,r)=>{"use strict";r.d(s,{BT:()=>o,Wu:()=>l,ZB:()=>d,Zp:()=>i,aR:()=>n});var t=r(5155);r(2115);var a=r(9434);function i(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...r})}function n(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...r})}function d(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",s),...r})}function o(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",s),...r})}function l(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",s),...r})}},9434:(e,s,r)=>{"use strict";r.d(s,{cn:()=>i});var t=r(2596),a=r(9688);function i(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,a.QP)((0,t.$)(s))}},9687:(e,s,r)=>{Promise.resolve().then(r.bind(r,1470)),Promise.resolve().then(r.bind(r,2972))}},e=>{e.O(0,[96,358],()=>e(e.s=9687)),_N_E=e.O()}]);