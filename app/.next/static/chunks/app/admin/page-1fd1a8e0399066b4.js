(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[698],{285:(e,s,a)=>{"use strict";a.d(s,{$:()=>d});var t=a(5155);a(2115);var r=a(9708),n=a(2085),i=a(9434);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:s,variant:a,size:n,asChild:d=!1,...o}=e,c=d?r.DX:"button";return(0,t.jsx)(c,{"data-slot":"button",className:(0,i.cn)(l({variant:a,size:n,className:s})),...o})}},1001:(e,s,a)=>{"use strict";a.d(s,{n:()=>n});var t=a(5453),r=a(6786);let n=(0,t.v)()((0,r.Zr)((e,s)=>({user:null,isAuthenticated:!1,isLoading:!1,login:s=>{e({user:s,isAuthenticated:!0,isLoading:!1})},logout:()=>{e({user:null,isAuthenticated:!1,isLoading:!1})},setLoading:s=>{e({isLoading:s})},updateUser:a=>{let t=s().user;t&&e({user:{...t,...a}})}}),{name:"auth-storage",partialize:e=>({user:e.user,isAuthenticated:e.isAuthenticated})}))},1470:(e,s,a)=>{"use strict";a.d(s,{AuthGuard:()=>l});var t=a(5155),r=a(2115),n=a(5695),i=a(1001);function l(e){let{children:s,requireAuth:a=!0,requireAdmin:l=!1,redirectTo:d}=e,{isAuthenticated:o,user:c,isLoading:u}=(0,i.n)(),x=(0,n.useRouter)();return((0,r.useEffect)(()=>{if(!u){if(a&&!o)return void x.push(d||"/login");if(l&&(null==c?void 0:c.role)!=="admin"||!a&&o)return void x.push("/dashboard")}},[o,c,u,a,l,x,d]),u)?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"})}):a&&!o||l&&(null==c?void 0:c.role)!=="admin"||!a&&o?null:(0,t.jsx)(t.Fragment,{children:s})}},2523:(e,s,a)=>{"use strict";a.d(s,{p:()=>n});var t=a(5155);a(2115);var r=a(9434);function n(e){let{className:s,type:a,...n}=e;return(0,t.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...n})}},3462:(e,s,a)=>{"use strict";a.d(s,{AdminDashboard:()=>J});var t=a(5155),r=a(2115),n=a(2177),i=a(221),l=a(8309),d=a(6671),o=a(3464),c=a(5936),u=a(707),x=a(285),m=a(2523),h=a(5057),p=a(6695),f=a(4165),g=a(9434);function b(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,t.jsx)("table",{"data-slot":"table",className:(0,g.cn)("w-full caption-bottom text-sm",s),...a})})}function j(e){let{className:s,...a}=e;return(0,t.jsx)("thead",{"data-slot":"table-header",className:(0,g.cn)("[&_tr]:border-b",s),...a})}function v(e){let{className:s,...a}=e;return(0,t.jsx)("tbody",{"data-slot":"table-body",className:(0,g.cn)("[&_tr:last-child]:border-0",s),...a})}function y(e){let{className:s,...a}=e;return(0,t.jsx)("tr",{"data-slot":"table-row",className:(0,g.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",s),...a})}function w(e){let{className:s,...a}=e;return(0,t.jsx)("th",{"data-slot":"table-head",className:(0,g.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...a})}function N(e){let{className:s,...a}=e;return(0,t.jsx)("td",{"data-slot":"table-cell",className:(0,g.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...a})}var k=a(1154),A=a(3904),_=a(4616),z=a(9803),C=a(2525),S=a(1001);let R=l.Ik({username:l.Yj().min(3,"用户名至少3个字符").max(50,"用户名最多50个字符"),email:l.Yj().email("邮箱格式不正确"),password:l.Yj().min(6,"密码至少6个字符"),role:l.k5(["admin","user"]),auth_code:l.Yj().optional()}),P=l.Ik({newPassword:l.Yj().min(6,"密码至少6个字符")});function J(){let[e,s]=(0,r.useState)([]),[a,l]=(0,r.useState)(!0),[g,J]=(0,r.useState)(!1),[L,$]=(0,r.useState)(!1),[E,F]=(0,r.useState)(null),[B,O]=(0,r.useState)(!1),[Z,Y]=(0,r.useState)(!1),{user:G}=(0,S.n)(),{register:T,handleSubmit:D,formState:{errors:V},reset:W}=(0,n.mN)({resolver:(0,i.u)(R),defaultValues:{role:"user"}}),{register:H,handleSubmit:I,formState:{errors:M},reset:U}=(0,n.mN)({resolver:(0,i.u)(P)}),Q=async()=>{if(G)try{l(!0);let e=await o.A.get("/api/admin/users",{headers:{Authorization:"Bearer ".concat(JSON.stringify(G))}});e.data.success?s(e.data.data):d.oR.error("获取用户列表失败")}catch(s){var e,a;console.error("获取用户列表错误:",s),d.oR.error((null==(a=s.response)||null==(e=a.data)?void 0:e.error)||"获取用户列表失败")}finally{l(!1)}},X=async a=>{if(G)try{O(!0);let t=await o.A.post("/api/admin/users",a,{headers:{Authorization:"Bearer ".concat(JSON.stringify(G)),"Content-Type":"application/json"}});t.data.success?(d.oR.success("用户创建成功"),s([t.data.data,...e]),J(!1),W()):d.oR.error(t.data.error||"创建失败")}catch(e){var t,r;console.error("创建用户错误:",e),d.oR.error((null==(r=e.response)||null==(t=r.data)?void 0:t.error)||"创建失败")}finally{O(!1)}},q=async e=>{if(E&&G)try{Y(!0);let s=await o.A.patch("/api/admin/users/".concat(E.id),{newPassword:e.newPassword},{headers:{Authorization:"Bearer ".concat(JSON.stringify(G)),"Content-Type":"application/json"}});s.data.success?(d.oR.success("密码重置成功"),$(!1),F(null),U()):d.oR.error(s.data.error||"重置失败")}catch(e){var s,a;console.error("重置密码错误:",e),d.oR.error((null==(a=e.response)||null==(s=a.data)?void 0:s.error)||"重置失败")}finally{Y(!1)}},K=async a=>{if(G&&confirm("确定要删除这个用户吗？此操作无法撤销。"))try{(await o.A.delete("/api/admin/users/".concat(a),{headers:{Authorization:"Bearer ".concat(JSON.stringify(G))}})).data.success?(d.oR.success("用户删除成功"),s(e.filter(e=>e.id!==a))):d.oR.error("删除失败")}catch(e){var t,r;console.error("删除用户错误:",e),d.oR.error((null==(r=e.response)||null==(t=r.data)?void 0:t.error)||"删除失败")}};return((0,r.useEffect)(()=>{Q()},[G]),a)?(0,t.jsx)(p.Zp,{children:(0,t.jsxs)(p.Wu,{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(k.A,{className:"w-6 h-6 animate-spin mr-2"}),"加载中..."]})}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold",children:"用户管理"}),(0,t.jsx)("p",{className:"text-gray-600",children:"管理系统用户和权限"})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(x.$,{variant:"outline",onClick:Q,disabled:a,children:[(0,t.jsx)(A.A,{className:"w-4 h-4 mr-2"}),"刷新"]}),(0,t.jsxs)(x.$,{onClick:()=>J(!0),children:[(0,t.jsx)(_.A,{className:"w-4 h-4 mr-2"}),"创建用户"]})]})]}),(0,t.jsxs)(p.Zp,{children:[(0,t.jsxs)(p.aR,{children:[(0,t.jsx)(p.ZB,{children:"用户列表"}),(0,t.jsx)(p.BT,{children:"系统中的所有用户"})]}),(0,t.jsx)(p.Wu,{children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)(b,{children:[(0,t.jsx)(j,{children:(0,t.jsxs)(y,{children:[(0,t.jsx)(w,{children:"用户名"}),(0,t.jsx)(w,{className:"hidden sm:table-cell",children:"邮箱"}),(0,t.jsx)(w,{children:"角色"}),(0,t.jsx)(w,{className:"hidden md:table-cell",children:"授权码"}),(0,t.jsx)(w,{className:"hidden lg:table-cell",children:"创建时间"}),(0,t.jsx)(w,{children:"操作"})]})}),(0,t.jsx)(v,{children:e.map(e=>(0,t.jsxs)(y,{children:[(0,t.jsx)(N,{className:"font-medium",children:e.username}),(0,t.jsx)(N,{className:"hidden sm:table-cell",children:e.email}),(0,t.jsx)(N,{children:(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs ".concat("admin"===e.role?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"),children:"admin"===e.role?"管理员":"用户"})}),(0,t.jsx)(N,{className:"hidden md:table-cell",children:e.auth_code||"-"}),(0,t.jsx)(N,{className:"hidden lg:table-cell",children:(0,c.GP)(new Date(e.created_at),"yyyy-MM-dd HH:mm",{locale:u.g})}),(0,t.jsx)(N,{children:(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsx)(x.$,{variant:"ghost",size:"sm",onClick:()=>{F(e),$(!0)},children:(0,t.jsx)(z.A,{className:"w-4 h-4"})}),e.id!==(null==G?void 0:G.id)&&(0,t.jsx)(x.$,{variant:"ghost",size:"sm",onClick:()=>K(e.id),children:(0,t.jsx)(C.A,{className:"w-4 h-4 text-red-500"})})]})})]},e.id))})]})})})]}),(0,t.jsx)(f.lG,{open:g,onOpenChange:J,children:(0,t.jsxs)(f.Cf,{children:[(0,t.jsxs)(f.c7,{children:[(0,t.jsx)(f.L3,{children:"创建新用户"}),(0,t.jsx)(f.rr,{children:"填写用户信息创建新账号"})]}),(0,t.jsxs)("form",{onSubmit:D(X),className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"username",children:"用户名"}),(0,t.jsx)(m.p,{id:"username",...T("username"),disabled:B}),V.username&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:V.username.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"email",children:"邮箱"}),(0,t.jsx)(m.p,{id:"email",type:"email",...T("email"),disabled:B}),V.email&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:V.email.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"password",children:"密码"}),(0,t.jsx)(m.p,{id:"password",type:"password",...T("password"),disabled:B}),V.password&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:V.password.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"auth_code",children:"授权码（可选）"}),(0,t.jsx)(m.p,{id:"auth_code",...T("auth_code"),disabled:B})]}),(0,t.jsxs)(f.Es,{children:[(0,t.jsx)(x.$,{type:"button",variant:"outline",onClick:()=>J(!1),disabled:B,children:"取消"}),(0,t.jsx)(x.$,{type:"submit",disabled:B,children:B?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(k.A,{className:"w-4 h-4 mr-2 animate-spin"}),"创建中..."]}):"创建用户"})]})]})]})}),(0,t.jsx)(f.lG,{open:L,onOpenChange:$,children:(0,t.jsxs)(f.Cf,{children:[(0,t.jsxs)(f.c7,{children:[(0,t.jsx)(f.L3,{children:"重置密码"}),(0,t.jsxs)(f.rr,{children:["为用户 ",null==E?void 0:E.username," 设置新密码"]})]}),(0,t.jsxs)("form",{onSubmit:I(q),className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"newPassword",children:"新密码"}),(0,t.jsx)(m.p,{id:"newPassword",type:"password",...H("newPassword"),disabled:Z}),M.newPassword&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:M.newPassword.message})]}),(0,t.jsxs)(f.Es,{children:[(0,t.jsx)(x.$,{type:"button",variant:"outline",onClick:()=>$(!1),disabled:Z,children:"取消"}),(0,t.jsx)(x.$,{type:"submit",disabled:Z,children:Z?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(k.A,{className:"w-4 h-4 mr-2 animate-spin"}),"重置中..."]}):"重置密码"})]})]})]})})]})}},4165:(e,s,a)=>{"use strict";a.d(s,{Cf:()=>c,Es:()=>x,L3:()=>m,c7:()=>u,lG:()=>l,rr:()=>h});var t=a(5155);a(2115);var r=a(237),n=a(4416),i=a(9434);function l(e){let{...s}=e;return(0,t.jsx)(r.bL,{"data-slot":"dialog",...s})}function d(e){let{...s}=e;return(0,t.jsx)(r.ZL,{"data-slot":"dialog-portal",...s})}function o(e){let{className:s,...a}=e;return(0,t.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",s),...a})}function c(e){let{className:s,children:a,showCloseButton:l=!0,...c}=e;return(0,t.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,t.jsx)(o,{}),(0,t.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",s),...c,children:[a,l&&(0,t.jsxs)(r.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(n.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",s),...a})}function x(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",s),...a})}function m(e){let{className:s,...a}=e;return(0,t.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",s),...a})}function h(e){let{className:s,...a}=e;return(0,t.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",s),...a})}},5057:(e,s,a)=>{"use strict";a.d(s,{J:()=>i});var t=a(5155);a(2115);var r=a(968),n=a(9434);function i(e){let{className:s,...a}=e;return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...a})}},6695:(e,s,a)=>{"use strict";a.d(s,{BT:()=>d,Wu:()=>o,ZB:()=>l,Zp:()=>n,aR:()=>i});var t=a(5155);a(2115);var r=a(9434);function n(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...a})}function i(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...a})}function l(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",s),...a})}function d(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",s),...a})}function o(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",s),...a})}},9434:(e,s,a)=>{"use strict";a.d(s,{cn:()=>n});var t=a(2596),r=a(9688);function n(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,r.QP)((0,t.$)(s))}},9556:(e,s,a)=>{Promise.resolve().then(a.bind(a,3462)),Promise.resolve().then(a.bind(a,1470))}},e=>{e.O(0,[96,358],()=>e(e.s=9556)),_N_E=e.O()}]);