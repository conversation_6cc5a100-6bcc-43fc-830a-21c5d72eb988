(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{285:(e,r,t)=>{"use strict";t.d(r,{$:()=>o});var s=t(5155);t(2115);var a=t(9708),i=t(2085),n=t(9434);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:r,variant:t,size:i,asChild:o=!1,...l}=e,u=o?a.DX:"button";return(0,s.jsx)(u,{"data-slot":"button",className:(0,n.cn)(d({variant:t,size:i,className:r})),...l})}},1001:(e,r,t)=>{"use strict";t.d(r,{n:()=>i});var s=t(5453),a=t(6786);let i=(0,s.v)()((0,a.Zr)((e,r)=>({user:null,isAuthenticated:!1,isLoading:!1,login:r=>{e({user:r,isAuthenticated:!0,isLoading:!1})},logout:()=>{e({user:null,isAuthenticated:!1,isLoading:!1})},setLoading:r=>{e({isLoading:r})},updateUser:t=>{let s=r().user;s&&e({user:{...s,...t}})}}),{name:"auth-storage",partialize:e=>({user:e.user,isAuthenticated:e.isAuthenticated})}))},1470:(e,r,t)=>{"use strict";t.d(r,{AuthGuard:()=>d});var s=t(5155),a=t(2115),i=t(5695),n=t(1001);function d(e){let{children:r,requireAuth:t=!0,requireAdmin:d=!1,redirectTo:o}=e,{isAuthenticated:l,user:u,isLoading:c}=(0,n.n)(),p=(0,i.useRouter)();return((0,a.useEffect)(()=>{if(!c){if(t&&!l)return void p.push(o||"/login");if(d&&(null==u?void 0:u.role)!=="admin"||!t&&l)return void p.push("/dashboard")}},[l,u,c,t,d,p,o]),c)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"})}):t&&!l||d&&(null==u?void 0:u.role)!=="admin"||!t&&l?null:(0,s.jsx)(s.Fragment,{children:r})}},2470:(e,r,t)=>{"use strict";t.d(r,{LoginForm:()=>h});var s=t(5155),a=t(2115),i=t(5695),n=t(2177),d=t(221),o=t(8309),l=t(3464),u=t(6671),c=t(285),p=t(2523),m=t(5057),v=t(6695),x=t(1001);let g=o.Ik({username:o.Yj().min(1,"用户名不能为空"),password:o.Yj().min(1,"密码不能为空")});function h(){let[e,r]=(0,a.useState)(!1),t=(0,i.useRouter)(),{login:o}=(0,x.n)(),{register:h,handleSubmit:b,formState:{errors:f}}=(0,n.mN)({resolver:(0,d.u)(g)}),j=async e=>{r(!0);try{let r=await l.A.post("/api/auth/login",e);r.data.success?(o(r.data.data),u.oR.success("登录成功"),t.push("/dashboard")):u.oR.error(r.data.error||"登录失败")}catch(e){var s,a;console.error("登录错误:",e),u.oR.error((null==(a=e.response)||null==(s=a.data)?void 0:s.error)||"登录失败，请稍后重试")}finally{r(!1)}};return(0,s.jsxs)(v.Zp,{className:"w-full max-w-md mx-auto",children:[(0,s.jsxs)(v.aR,{children:[(0,s.jsx)(v.ZB,{children:"登录"}),(0,s.jsx)(v.BT,{children:"输入您的用户名和密码来登录系统"})]}),(0,s.jsxs)(v.Wu,{children:[(0,s.jsxs)("form",{onSubmit:b(j),className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(m.J,{htmlFor:"username",children:"用户名"}),(0,s.jsx)(p.p,{id:"username",type:"text",placeholder:"请输入用户名",...h("username"),disabled:e}),f.username&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:f.username.message})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(m.J,{htmlFor:"password",children:"密码"}),(0,s.jsx)(p.p,{id:"password",type:"password",placeholder:"请输入密码",...h("password"),disabled:e}),f.password&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:f.password.message})]}),(0,s.jsx)(c.$,{type:"submit",className:"w-full",disabled:e,children:e?"登录中...":"登录"})]}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["还没有账号？"," ",(0,s.jsx)("button",{type:"button",className:"text-blue-600 hover:underline",onClick:()=>t.push("/register"),children:"立即注册"})]})})]})]})}},2523:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var s=t(5155);t(2115);var a=t(9434);function i(e){let{className:r,type:t,...i}=e;return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...i})}},4558:(e,r,t)=>{Promise.resolve().then(t.bind(t,1470)),Promise.resolve().then(t.bind(t,2470))},5057:(e,r,t)=>{"use strict";t.d(r,{J:()=>n});var s=t(5155);t(2115);var a=t(968),i=t(9434);function n(e){let{className:r,...t}=e;return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...t})}},6695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>o,Wu:()=>l,ZB:()=>d,Zp:()=>i,aR:()=>n});var s=t(5155);t(2115);var a=t(9434);function i(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...t})}function n(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function d(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",r),...t})}function o(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",r),...t})}function l(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",r),...t})}},9434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>i});var s=t(2596),a=t(9688);function i(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}}},e=>{e.O(0,[96,358],()=>e(e.s=4558)),_N_E=e.O()}]);