(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{285:(e,s,a)=>{"use strict";a.d(s,{$:()=>d});var t=a(5155);a(2115);var i=a(9708),r=a(2085),n=a(9434);let l=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:s,variant:a,size:r,asChild:d=!1,...c}=e,o=d?i.DX:"button";return(0,t.jsx)(o,{"data-slot":"button",className:(0,n.cn)(l({variant:a,size:r,className:s})),...c})}},997:(e,s,a)=>{Promise.resolve().then(a.bind(a,1470)),Promise.resolve().then(a.bind(a,4253))},1001:(e,s,a)=>{"use strict";a.d(s,{n:()=>r});var t=a(5453),i=a(6786);let r=(0,t.v)()((0,i.Zr)((e,s)=>({user:null,isAuthenticated:!1,isLoading:!1,login:s=>{e({user:s,isAuthenticated:!0,isLoading:!1})},logout:()=>{e({user:null,isAuthenticated:!1,isLoading:!1})},setLoading:s=>{e({isLoading:s})},updateUser:a=>{let t=s().user;t&&e({user:{...t,...a}})}}),{name:"auth-storage",partialize:e=>({user:e.user,isAuthenticated:e.isAuthenticated})}))},1470:(e,s,a)=>{"use strict";a.d(s,{AuthGuard:()=>l});var t=a(5155),i=a(2115),r=a(5695),n=a(1001);function l(e){let{children:s,requireAuth:a=!0,requireAdmin:l=!1,redirectTo:d}=e,{isAuthenticated:c,user:o,isLoading:u}=(0,n.n)(),m=(0,r.useRouter)();return((0,i.useEffect)(()=>{if(!u){if(a&&!c)return void m.push(d||"/login");if(l&&(null==o?void 0:o.role)!=="admin"||!a&&c)return void m.push("/dashboard")}},[c,o,u,a,l,m,d]),u)?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"})}):a&&!c||l&&(null==o?void 0:o.role)!=="admin"||!a&&c?null:(0,t.jsx)(t.Fragment,{children:s})}},2523:(e,s,a)=>{"use strict";a.d(s,{p:()=>r});var t=a(5155);a(2115);var i=a(9434);function r(e){let{className:s,type:a,...r}=e;return(0,t.jsx)("input",{type:a,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...r})}},4165:(e,s,a)=>{"use strict";a.d(s,{Cf:()=>o,Es:()=>m,L3:()=>x,c7:()=>u,lG:()=>l,rr:()=>p});var t=a(5155);a(2115);var i=a(237),r=a(4416),n=a(9434);function l(e){let{...s}=e;return(0,t.jsx)(i.bL,{"data-slot":"dialog",...s})}function d(e){let{...s}=e;return(0,t.jsx)(i.ZL,{"data-slot":"dialog-portal",...s})}function c(e){let{className:s,...a}=e;return(0,t.jsx)(i.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",s),...a})}function o(e){let{className:s,children:a,showCloseButton:l=!0,...o}=e;return(0,t.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,t.jsx)(c,{}),(0,t.jsxs)(i.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",s),...o,children:[a,l&&(0,t.jsxs)(i.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(r.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",s),...a})}function m(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",s),...a})}function x(e){let{className:s,...a}=e;return(0,t.jsx)(i.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",s),...a})}function p(e){let{className:s,...a}=e;return(0,t.jsx)(i.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",s),...a})}},4253:(e,s,a)=>{"use strict";a.d(s,{DashboardContent:()=>K});var t=a(5155),i=a(2115),r=a(5695),n=a(285),l=a(6695),d=a(4835),c=a(4616),o=a(9676),u=a(381),m=a(1001);let x=(0,a(5453).v)(e=>({currentInput:"",currentInputType:"text",currentImageUrl:void 0,analysisResult:null,calorieResult:null,dietRecords:[],isAnalyzing:!1,isSubmitting:!1,setCurrentInput:(s,a,t)=>e({currentInput:s,currentInputType:a,currentImageUrl:t}),setAnalysisResult:s=>e({analysisResult:s}),setCalorieResult:s=>e({calorieResult:s}),setDietRecords:s=>e({dietRecords:s}),addDietRecord:s=>e(e=>({dietRecords:[s,...e.dietRecords]})),removeDietRecord:s=>e(e=>({dietRecords:e.dietRecords.filter(e=>e.id!==s)})),setIsAnalyzing:s=>e({isAnalyzing:s}),setIsSubmitting:s=>e({isSubmitting:s}),reset:()=>e({currentInput:"",currentInputType:"text",currentImageUrl:void 0,analysisResult:null,calorieResult:null,isAnalyzing:!1,isSubmitting:!1})}));var p=a(2177),h=a(221),g=a(8309),j=a(6671),f=a(3464),v=a(6766),y=a(2523),b=a(5057),N=a(9434);function w(e){let{className:s,...a}=e;return(0,t.jsx)("textarea",{"data-slot":"textarea",className:(0,N.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),...a})}var _=a(4355),A=a(9869),k=a(1154);let z=g.Ik({input:g.Yj().min(1,"请输入饮食内容或上传图片")});function R(){let[e,s]=(0,i.useState)("text"),[a,r]=(0,i.useState)(null),[d,c]=(0,i.useState)(null),o=(0,i.useRef)(null),{user:u}=(0,m.n)(),{setCurrentInput:g,setIsAnalyzing:N,isAnalyzing:R}=x(),{register:C,handleSubmit:S,formState:{errors:I},reset:J}=(0,p.mN)({resolver:(0,h.u)(z)}),$=async t=>{if(!u)return void j.oR.error("请先登录");N(!0);try{let i;if("image"===e&&a){let e=new FormData;e.append("file",a);let s=await f.A.post("/api/upload/image",e,{headers:{Authorization:"Bearer ".concat(JSON.stringify(u)),"Content-Type":"multipart/form-data"}});if(s.data.success)i=s.data.data.thumbnailUrl;else throw Error("图片上传失败")}let n=await f.A.post("/api/ai/analyze-diet",{input:t.input,inputType:e,imageUrl:i},{headers:{Authorization:"Bearer ".concat(JSON.stringify(u)),"Content-Type":"application/json"}});if(n.data.success)g(t.input,e,i),x.getState().setAnalysisResult(n.data.data),j.oR.success("分析完成"),J(),r(null),c(null),s("text");else throw Error(n.data.error||"分析失败")}catch(e){var i,n;console.error("饮食分析错误:",e),j.oR.error((null==(n=e.response)||null==(i=n.data)?void 0:i.error)||"分析失败，请稍后重试")}finally{N(!1)}};return(0,t.jsxs)(l.Zp,{className:"w-full max-w-2xl mx-auto",children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"饮食记录"}),(0,t.jsx)(l.BT,{children:"输入您的饮食内容或上传食物图片，AI将为您分析食材和热量"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("form",{onSubmit:S($),className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)(n.$,{type:"button",variant:"text"===e?"default":"outline",onClick:()=>s("text"),className:"flex-1",children:"文字输入"}),(0,t.jsxs)(n.$,{type:"button",variant:"image"===e?"default":"outline",onClick:()=>s("image"),className:"flex-1",children:[(0,t.jsx)(_.A,{className:"w-4 h-4 mr-2"}),"图片上传"]})]}),"text"===e&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(b.J,{htmlFor:"input",children:"饮食描述"}),(0,t.jsx)(w,{id:"input",placeholder:"请描述您吃了什么，例如：一碗米饭，红烧肉，青菜...",...C("input"),disabled:R,rows:4}),I.input&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:I.input.message})]}),"image"===e&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(b.J,{htmlFor:"input",children:"补充说明（可选）"}),(0,t.jsx)(y.p,{id:"input",placeholder:"可以补充说明食物的具体信息...",...C("input"),disabled:R})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(b.J,{children:"上传食物图片"}),(0,t.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center",children:[d?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(v.default,{src:d,alt:"预览",width:400,height:300,className:"max-w-full max-h-64 mx-auto rounded-lg object-contain"}),(0,t.jsx)(n.$,{type:"button",variant:"outline",onClick:()=>{r(null),c(null),o.current&&(o.current.value="")},children:"重新选择"})]}):(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(A.A,{className:"w-12 h-12 mx-auto text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)(n.$,{type:"button",variant:"outline",onClick:()=>{var e;return null==(e=o.current)?void 0:e.click()},children:"选择图片"}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"支持 JPEG、PNG、WebP 格式，最大 10MB"})]})]}),(0,t.jsx)("input",{ref:o,type:"file",accept:"image/jpeg,image/jpg,image/png,image/webp",onChange:e=>{var s;let a=null==(s=e.target.files)?void 0:s[0];if(a){if(!["image/jpeg","image/jpg","image/png","image/webp"].includes(a.type))return void j.oR.error("只支持 JPEG、PNG、WebP 格式的图片");if(a.size>0xa00000)return void j.oR.error("图片大小不能超过 10MB");r(a);let e=new FileReader;e.onload=e=>{var s;c(null==(s=e.target)?void 0:s.result)},e.readAsDataURL(a)}},className:"hidden"})]})]})]}),(0,t.jsx)(n.$,{type:"submit",className:"w-full",disabled:R||"image"===e&&!a,children:R?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(k.A,{className:"w-4 h-4 mr-2 animate-spin"}),"分析中..."]}):"开始分析"})]})})]})}var C=a(5713),S=a(6474),I=a(5196),J=a(7863);function $(e){let{...s}=e;return(0,t.jsx)(C.bL,{"data-slot":"select",...s})}function B(e){let{...s}=e;return(0,t.jsx)(C.WT,{"data-slot":"select-value",...s})}function P(e){let{className:s,size:a="default",children:i,...r}=e;return(0,t.jsxs)(C.l9,{"data-slot":"select-trigger","data-size":a,className:(0,N.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...r,children:[i,(0,t.jsx)(C.In,{asChild:!0,children:(0,t.jsx)(S.A,{className:"size-4 opacity-50"})})]})}function F(e){let{className:s,children:a,position:i="popper",...r}=e;return(0,t.jsx)(C.ZL,{children:(0,t.jsxs)(C.UC,{"data-slot":"select-content",className:(0,N.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:i,...r,children:[(0,t.jsx)(E,{}),(0,t.jsx)(C.LM,{className:(0,N.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,t.jsx)(O,{})]})})}function Z(e){let{className:s,children:a,...i}=e;return(0,t.jsxs)(C.q7,{"data-slot":"select-item",className:(0,N.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",s),...i,children:[(0,t.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,t.jsx)(C.VF,{children:(0,t.jsx)(I.A,{className:"size-4"})})}),(0,t.jsx)(C.p4,{children:a})]})}function E(e){let{className:s,...a}=e;return(0,t.jsx)(C.PP,{"data-slot":"select-scroll-up-button",className:(0,N.cn)("flex cursor-default items-center justify-center py-1",s),...a,children:(0,t.jsx)(J.A,{className:"size-4"})})}function O(e){let{className:s,...a}=e;return(0,t.jsx)(C.wn,{"data-slot":"select-scroll-down-button",className:(0,N.cn)("flex cursor-default items-center justify-center py-1",s),...a,children:(0,t.jsx)(S.A,{className:"size-4"})})}var L=a(2525);let T=g.Ik({ingredients:g.YO(g.Ik({name:g.Yj().min(1,"食材名称不能为空"),amount:g.ai().min(.1,"数量必须大于0"),unit:g.Yj().min(1,"单位不能为空")})),dishes:g.YO(g.Ik({name:g.Yj().min(1,"菜品名称不能为空"),category:g.k5(["dish","staple","drink","side"])})),meal_type:g.k5(["breakfast","lunch","dinner","snack"]),record_time:g.Yj()});function W(){let{user:e}=(0,m.n)(),{analysisResult:s,currentInput:a,currentInputType:r,currentImageUrl:d,setIsSubmitting:o,isSubmitting:u,reset:g}=x(),{register:v,control:N,handleSubmit:w,formState:{errors:_},setValue:A,watch:z}=(0,p.mN)({resolver:(0,h.u)(T)}),{fields:R,append:C,remove:S}=(0,p.jz)({control:N,name:"ingredients"}),{fields:I,append:J,remove:E}=(0,p.jz)({control:N,name:"dishes"});(0,i.useEffect)(()=>{s&&(A("ingredients",s.ingredients),A("dishes",s.dishes),A("meal_type",s.meal_type),A("record_time",s.estimated_time))},[s,A]);let O=async s=>{if(!e)return void j.oR.error("请先登录");o(!0);try{let t=await f.A.post("/api/ai/analyze-calories",{ingredients:s.ingredients},{headers:{Authorization:"Bearer ".concat(JSON.stringify(e)),"Content-Type":"application/json"}});if(!t.data.success)throw Error("热量分析失败");let i=t.data.data,n=await f.A.post("/api/diet/records",{original_input:a,input_type:r,image_url:d,meal_type:s.meal_type,record_time:s.record_time,total_calories:i.total_calories,ai_analysis:i.analysis,dishes:s.dishes,ingredients:s.ingredients.map((e,s)=>{var a;return{...e,calories_per_100g:(null==(a=i.breakdown[s])?void 0:a.calories)||0}})},{headers:{Authorization:"Bearer ".concat(JSON.stringify(e)),"Content-Type":"application/json"}});if(n.data.success)j.oR.success("饮食记录保存成功"),g();else throw Error(n.data.error||"保存失败")}catch(e){var t,i;console.error("保存饮食记录错误:",e),j.oR.error((null==(i=e.response)||null==(t=i.data)?void 0:t.error)||"保存失败，请稍后重试")}finally{o(!1)}};return s?(0,t.jsxs)(l.Zp,{className:"w-full max-w-4xl mx-auto",children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"确认分析结果"}),(0,t.jsx)(l.BT,{children:"请检查并修改AI分析的结果，确保信息准确"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("form",{onSubmit:w(O),className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(b.J,{children:"餐次"}),(0,t.jsxs)($,{value:z("meal_type"),onValueChange:e=>A("meal_type",e),children:[(0,t.jsx)(P,{children:(0,t.jsx)(B,{placeholder:"选择餐次"})}),(0,t.jsxs)(F,{children:[(0,t.jsx)(Z,{value:"breakfast",children:"早餐"}),(0,t.jsx)(Z,{value:"lunch",children:"午餐"}),(0,t.jsx)(Z,{value:"dinner",children:"晚餐"}),(0,t.jsx)(Z,{value:"snack",children:"加餐"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(b.J,{htmlFor:"record_time",children:"记录时间"}),(0,t.jsx)(y.p,{id:"record_time",type:"datetime-local",...v("record_time"),disabled:u})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(b.J,{className:"text-lg font-semibold",children:"食材清单"}),(0,t.jsxs)(n.$,{type:"button",variant:"outline",size:"sm",onClick:()=>C({name:"",amount:0,unit:"g"}),children:[(0,t.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"添加食材"]})]}),R.map((e,s)=>{var a,i,r,l,d,c,o,m,x,p,h,g;return(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(b.J,{children:"食材名称"}),(0,t.jsx)(y.p,{...v("ingredients.".concat(s,".name")),placeholder:"食材名称",disabled:u}),(null==(i=_.ingredients)||null==(a=i[s])?void 0:a.name)&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:null==(l=_.ingredients[s])||null==(r=l.name)?void 0:r.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(b.J,{children:"数量"}),(0,t.jsx)(y.p,{type:"number",step:"0.1",...v("ingredients.".concat(s,".amount"),{valueAsNumber:!0}),placeholder:"数量",disabled:u}),(null==(c=_.ingredients)||null==(d=c[s])?void 0:d.amount)&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:null==(m=_.ingredients[s])||null==(o=m.amount)?void 0:o.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(b.J,{children:"单位"}),(0,t.jsx)(y.p,{...v("ingredients.".concat(s,".unit")),placeholder:"单位",disabled:u}),(null==(p=_.ingredients)||null==(x=p[s])?void 0:x.unit)&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:null==(g=_.ingredients[s])||null==(h=g.unit)?void 0:h.message})]}),(0,t.jsx)("div",{className:"flex items-end",children:(0,t.jsx)(n.$,{type:"button",variant:"outline",size:"sm",onClick:()=>S(s),disabled:u,children:(0,t.jsx)(L.A,{className:"w-4 h-4"})})})]},e.id)})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(b.J,{className:"text-lg font-semibold",children:"菜品清单"}),(0,t.jsxs)(n.$,{type:"button",variant:"outline",size:"sm",onClick:()=>J({name:"",category:"dish"}),children:[(0,t.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"添加菜品"]})]}),I.map((e,s)=>{var a,i,r,l;return(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(b.J,{children:"菜品名称"}),(0,t.jsx)(y.p,{...v("dishes.".concat(s,".name")),placeholder:"菜品名称",disabled:u}),(null==(i=_.dishes)||null==(a=i[s])?void 0:a.name)&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:null==(l=_.dishes[s])||null==(r=l.name)?void 0:r.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(b.J,{children:"分类"}),(0,t.jsxs)($,{value:z("dishes.".concat(s,".category")),onValueChange:e=>A("dishes.".concat(s,".category"),e),children:[(0,t.jsx)(P,{children:(0,t.jsx)(B,{placeholder:"选择分类"})}),(0,t.jsxs)(F,{children:[(0,t.jsx)(Z,{value:"dish",children:"菜品"}),(0,t.jsx)(Z,{value:"staple",children:"主食"}),(0,t.jsx)(Z,{value:"drink",children:"饮料"}),(0,t.jsx)(Z,{value:"side",children:"配菜"})]})]})]}),(0,t.jsx)("div",{className:"flex items-end",children:(0,t.jsx)(n.$,{type:"button",variant:"outline",size:"sm",onClick:()=>E(s),disabled:u,children:(0,t.jsx)(L.A,{className:"w-4 h-4"})})})]},e.id)})]}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)(n.$,{type:"button",variant:"outline",onClick:g,disabled:u,className:"flex-1",children:"取消"}),(0,t.jsx)(n.$,{type:"submit",disabled:u,className:"flex-1",children:u?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(k.A,{className:"w-4 h-4 mr-2 animate-spin"}),"保存中..."]}):"确认保存"})]})]})})]}):null}var Y=a(5936),U=a(707),D=a(4165),G=a(3904),V=a(5747),M=a(4186);function H(){let[e,s]=(0,i.useState)([]),[a,r]=(0,i.useState)(!0),[d,c]=(0,i.useState)(!1),[o,u]=(0,i.useState)(null),[x,p]=(0,i.useState)(!1),{user:h}=(0,m.n)(),g=async()=>{if(h)try{r(!0);let e=await f.A.get("/api/diet/records",{headers:{Authorization:"Bearer ".concat(JSON.stringify(h))}});e.data.success?s(e.data.data):j.oR.error("获取历史记录失败")}catch(s){var e,a;console.error("获取历史记录错误:",s),j.oR.error((null==(a=s.response)||null==(e=a.data)?void 0:e.error)||"获取历史记录失败")}finally{r(!1)}},v=async()=>{if(o&&h)try{p(!0),(await f.A.delete("/api/diet/records/".concat(o.id),{headers:{Authorization:"Bearer ".concat(JSON.stringify(h))}})).data.success?(j.oR.success("记录删除成功"),s(e.filter(e=>e.id!==o.id)),c(!1),u(null)):j.oR.error("删除失败")}catch(e){var a,t;console.error("删除记录错误:",e),j.oR.error((null==(t=e.response)||null==(a=t.data)?void 0:a.error)||"删除失败")}finally{p(!1)}};return((0,i.useEffect)(()=>{g()},[h]),a)?(0,t.jsx)(l.Zp,{children:(0,t.jsxs)(l.Wu,{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(k.A,{className:"w-6 h-6 animate-spin mr-2"}),"加载中..."]})}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold",children:"饮食历史"}),(0,t.jsx)("p",{className:"text-gray-600",children:"查看您的饮食记录历史"})]}),(0,t.jsxs)(n.$,{variant:"outline",onClick:g,disabled:a,children:[(0,t.jsx)(G.A,{className:"w-4 h-4 mr-2"}),"刷新"]})]}),0===e.length?(0,t.jsx)(l.Zp,{children:(0,t.jsxs)(l.Wu,{className:"text-center py-8",children:[(0,t.jsx)(V.A,{className:"w-12 h-12 mx-auto text-gray-400 mb-4"}),(0,t.jsx)("p",{className:"text-gray-500",children:"还没有饮食记录"}),(0,t.jsx)("p",{className:"text-sm text-gray-400 mt-2",children:"开始记录您的第一餐吧！"})]})}):(0,t.jsx)("div",{className:"space-y-4",children:e.map(e=>{var s;return(0,t.jsxs)(l.Zp,{className:"hover:shadow-md transition-shadow",children:[(0,t.jsx)(l.aR,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(M.A,{className:"w-4 h-4 text-gray-500"}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:(0,Y.GP)(new Date(e.record_time),"yyyy年MM月dd日 HH:mm",{locale:U.g})}),(0,t.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full",children:{breakfast:"早餐",lunch:"午餐",dinner:"晚餐",snack:"加餐"}[s=e.meal_type]||s})]}),(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>{u(e),c(!0)},children:(0,t.jsx)(L.A,{className:"w-4 h-4 text-red-500"})})]})}),(0,t.jsxs)(l.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-sm text-gray-700 mb-2",children:"原始输入"}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-start space-y-2 sm:space-y-0 sm:space-x-3",children:["image"===e.input_type&&e.image_url&&(0,t.jsx)("img",{src:e.image_url,alt:"食物图片",className:"w-16 h-16 sm:w-20 sm:h-20 rounded-lg object-cover mx-auto sm:mx-0"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 flex-1 text-center sm:text-left",children:e.original_input})]})]}),e.ingredients&&e.ingredients.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-sm text-gray-700 mb-2",children:"食材清单"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:e.ingredients.map((e,s)=>(0,t.jsxs)("span",{className:"px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full",children:[e.name," ",e.amount,e.unit]},s))})]}),e.dishes&&e.dishes.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-sm text-gray-700 mb-2",children:"菜品清单"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:e.dishes.map((e,s)=>(0,t.jsx)("span",{className:"px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full",children:e},s))})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between pt-2 border-t space-y-2 sm:space-y-0",children:[(0,t.jsx)("div",{className:"flex items-center justify-center sm:justify-start space-x-4",children:(0,t.jsxs)("span",{className:"text-lg font-semibold text-red-600",children:[e.total_calories," 卡路里"]})}),e.ai_analysis&&(0,t.jsx)("p",{className:"text-sm text-gray-600 max-w-md text-center sm:text-right",children:e.ai_analysis})]})]})]},e.id)})}),(0,t.jsx)(D.lG,{open:d,onOpenChange:c,children:(0,t.jsxs)(D.Cf,{children:[(0,t.jsxs)(D.c7,{children:[(0,t.jsx)(D.L3,{children:"确认删除"}),(0,t.jsx)(D.rr,{children:"您确定要删除这条饮食记录吗？此操作无法撤销。"})]}),(0,t.jsxs)(D.Es,{children:[(0,t.jsx)(n.$,{variant:"outline",onClick:()=>c(!1),disabled:x,children:"取消"}),(0,t.jsx)(n.$,{variant:"destructive",onClick:v,disabled:x,children:x?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(k.A,{className:"w-4 h-4 mr-2 animate-spin"}),"删除中..."]}):"确认删除"})]})]})})]})}var q=a(5021);let Q=g.Ik({ai_model:g.Yj().min(1,"AI模型不能为空"),api_base_url:g.Yj().url("API地址格式不正确"),api_key:g.Yj().min(1,"API密钥不能为空")});function X(){let[e,s]=(0,i.useState)(!0),[a,r]=(0,i.useState)(!1),[d,c]=(0,i.useState)(!1),[o,u]=(0,i.useState)([]),[x,g]=(0,i.useState)(!1),{user:v}=(0,m.n)(),{register:N,handleSubmit:w,formState:{errors:_},setValue:A,watch:z,reset:R}=(0,p.mN)({resolver:(0,h.u)(Q),defaultValues:{ai_model:"gpt-4o",api_base_url:"https://api.openai.com",api_key:""}}),C=async()=>{if(v)try{s(!0);let e=await f.A.get("/api/user/settings",{headers:{Authorization:"Bearer ".concat(JSON.stringify(v))}});if(e.data.success&&e.data.data){let s=e.data.data;R({ai_model:s.ai_model,api_base_url:s.api_base_url,api_key:s.api_key})}}catch(e){console.error("获取设置错误:",e)}finally{s(!1)}},S=async()=>{if(v)try{g(!0);let e=await f.A.get("/api/ai/models",{headers:{Authorization:"Bearer ".concat(JSON.stringify(v))}});e.data.success?u(e.data.data):j.oR.error("获取模型列表失败")}catch(e){console.error("获取模型列表错误:",e),j.oR.error("获取模型列表失败，请检查API配置")}finally{g(!1)}},I=async()=>{if(!v)return;let e=z();try{c(!0),await f.A.put("/api/user/settings",e,{headers:{Authorization:"Bearer ".concat(JSON.stringify(v)),"Content-Type":"application/json"}});let s=await f.A.get("/api/ai/models",{headers:{Authorization:"Bearer ".concat(JSON.stringify(v))}});s.data.success?(j.oR.success("连接测试成功"),u(s.data.data)):j.oR.error("连接测试失败")}catch(e){var s,a;console.error("测试连接错误:",e),j.oR.error((null==(a=e.response)||null==(s=a.data)?void 0:s.error)||"连接测试失败")}finally{c(!1)}},J=async e=>{if(v)try{r(!0);let s=await f.A.put("/api/user/settings",e,{headers:{Authorization:"Bearer ".concat(JSON.stringify(v)),"Content-Type":"application/json"}});s.data.success?j.oR.success("设置保存成功"):j.oR.error(s.data.error||"保存失败")}catch(e){var s,a;console.error("保存设置错误:",e),j.oR.error((null==(a=e.response)||null==(s=a.data)?void 0:s.error)||"保存失败")}finally{r(!1)}};return((0,i.useEffect)(()=>{C()},[v]),e)?(0,t.jsx)(l.Zp,{children:(0,t.jsxs)(l.Wu,{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(k.A,{className:"w-6 h-6 animate-spin mr-2"}),"加载中..."]})}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold",children:"设置"}),(0,t.jsx)("p",{className:"text-gray-600",children:"配置您的AI模型和API设置"})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"AI模型配置"}),(0,t.jsx)(l.BT,{children:"配置用于饮食分析的AI模型和API设置"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("form",{onSubmit:w(J),className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(b.J,{htmlFor:"api_base_url",children:"API基础URL"}),(0,t.jsx)(y.p,{id:"api_base_url",type:"url",placeholder:"https://api.openai.com",...N("api_base_url"),disabled:a}),_.api_base_url&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:_.api_base_url.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(b.J,{htmlFor:"api_key",children:"API密钥"}),(0,t.jsx)(y.p,{id:"api_key",type:"password",placeholder:"输入您的API密钥",...N("api_key"),disabled:a}),_.api_key&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:_.api_key.message})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(n.$,{type:"button",variant:"outline",onClick:I,disabled:d||a,children:d?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(k.A,{className:"w-4 h-4 mr-2 animate-spin"}),"测试中..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(q.A,{className:"w-4 h-4 mr-2"}),"测试连接"]})}),(0,t.jsx)(n.$,{type:"button",variant:"outline",onClick:S,disabled:x||a,children:x?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(k.A,{className:"w-4 h-4 mr-2 animate-spin"}),"获取中..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(G.A,{className:"w-4 h-4 mr-2"}),"获取模型列表"]})})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(b.J,{children:"AI模型"}),(0,t.jsxs)($,{value:z("ai_model"),onValueChange:e=>A("ai_model",e),children:[(0,t.jsx)(P,{children:(0,t.jsx)(B,{placeholder:"选择AI模型"})}),(0,t.jsx)(F,{children:o.length>0?o.map(e=>(0,t.jsx)(Z,{value:e,children:e},e)):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(Z,{value:"gpt-4o",children:"gpt-4o"}),(0,t.jsx)(Z,{value:"gpt-4o-mini",children:"gpt-4o-mini"}),(0,t.jsx)(Z,{value:"gpt-4-turbo",children:"gpt-4-turbo"}),(0,t.jsx)(Z,{value:"gpt-3.5-turbo",children:"gpt-3.5-turbo"})]})})]}),_.ai_model&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:_.ai_model.message})]}),(0,t.jsx)(n.$,{type:"submit",disabled:a,className:"w-full",children:a?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(k.A,{className:"w-4 h-4 mr-2 animate-spin"}),"保存中..."]}):"保存设置"})]})})]})]})}function K(){let[e,s]=(0,i.useState)("input"),a=(0,r.useRouter)(),{user:p,logout:h}=(0,m.n)(),{analysisResult:g}=x();return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"饮食热量分析系统"})}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:["欢迎，",null==p?void 0:p.username]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>{h(),a.push("/login")},children:[(0,t.jsx)(d.A,{className:"w-4 h-4 mr-2"}),"退出登录"]})]})]})})}),(0,t.jsx)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,t.jsxs)("aside",{className:"w-full lg:w-64 space-y-2",children:[(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-4",children:(0,t.jsxs)("nav",{className:"space-y-2",children:[(0,t.jsxs)(n.$,{variant:"input"===e?"default":"ghost",className:"w-full justify-start",onClick:()=>s("input"),children:[(0,t.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"饮食记录"]}),(0,t.jsxs)(n.$,{variant:"history"===e?"default":"ghost",className:"w-full justify-start",onClick:()=>s("history"),children:[(0,t.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"历史记录"]}),(0,t.jsxs)(n.$,{variant:"settings"===e?"default":"ghost",className:"w-full justify-start",onClick:()=>s("settings"),children:[(0,t.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"设置"]})]})})}),(null==p?void 0:p.role)==="admin"&&(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{className:"pb-3",children:(0,t.jsx)(l.ZB,{className:"text-sm",children:"管理员功能"})}),(0,t.jsx)(l.Wu,{className:"pt-0",children:(0,t.jsx)(n.$,{variant:"outline",size:"sm",className:"w-full",onClick:()=>a.push("/admin"),children:"用户管理"})})]})]}),(0,t.jsxs)("div",{className:"flex-1",children:["input"===e&&(0,t.jsx)("div",{className:"space-y-8",children:g?(0,t.jsx)(W,{}):(0,t.jsx)(R,{})}),"history"===e&&(0,t.jsx)(H,{}),"settings"===e&&(0,t.jsx)(X,{})]})]})})]})}},5057:(e,s,a)=>{"use strict";a.d(s,{J:()=>n});var t=a(5155);a(2115);var i=a(968),r=a(9434);function n(e){let{className:s,...a}=e;return(0,t.jsx)(i.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...a})}},6695:(e,s,a)=>{"use strict";a.d(s,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>r,aR:()=>n});var t=a(5155);a(2115);var i=a(9434);function r(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...a})}function n(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...a})}function l(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",s),...a})}function d(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",s),...a})}function c(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",s),...a})}},9434:(e,s,a)=>{"use strict";a.d(s,{cn:()=>r});var t=a(2596),i=a(9688);function r(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,i.QP)((0,t.$)(s))}}},e=>{e.O(0,[96,358],()=>e(e.s=997)),_N_E=e.O()}]);