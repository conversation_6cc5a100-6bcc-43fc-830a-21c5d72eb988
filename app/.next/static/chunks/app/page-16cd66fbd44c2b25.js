(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{1001:(e,t,r)=>{"use strict";r.d(t,{n:()=>i});var n=r(5453),a=r(6786);let i=(0,n.v)()((0,a.Zr)((e,t)=>({user:null,isAuthenticated:!1,isLoading:!1,login:t=>{e({user:t,isAuthenticated:!0,isLoading:!1})},logout:()=>{e({user:null,isAuthenticated:!1,isLoading:!1})},setLoading:t=>{e({isLoading:t})},updateUser:r=>{let n=t().user;n&&e({user:{...n,...r}})}}),{name:"auth-storage",partialize:e=>({user:e.user,isAuthenticated:e.isAuthenticated})}))},3792:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var n=r(5155),a=r(2115),i=r(5695),s=r(1001);function u(){let e=(0,i.useRouter)(),{isAuthenticated:t,isLoading:r}=(0,s.n)();return(0,a.useEffect)(()=>{r||(t?e.push("/dashboard"):e.push("/login"))},[t,r,e]),(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"})})}},5453:(e,t,r)=>{"use strict";r.d(t,{v:()=>s});var n=r(2115);let a=e=>{let t,r=new Set,n=(e,n)=>{let a="function"==typeof e?e(t):e;if(!Object.is(a,t)){let e=t;t=(null!=n?n:"object"!=typeof a||null===a)?a:Object.assign({},t,a),r.forEach(r=>r(t,e))}},a=()=>t,i={setState:n,getState:a,getInitialState:()=>s,subscribe:e=>(r.add(e),()=>r.delete(e))},s=t=e(n,a,i);return i},i=e=>{let t=(e=>e?a(e):a)(e),r=e=>(function(e,t=e=>e){let r=n.useSyncExternalStore(e.subscribe,n.useCallback(()=>t(e.getState()),[e,t]),n.useCallback(()=>t(e.getInitialState()),[e,t]));return n.useDebugValue(r),r})(t,e);return Object.assign(r,t),r},s=e=>e?i(e):i},5695:(e,t,r)=>{"use strict";var n=r(8999);r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}})},6784:(e,t,r)=>{Promise.resolve().then(r.bind(r,3792))},6786:(e,t,r)=>{"use strict";r.d(t,{Zr:()=>a});let n=e=>t=>{try{let r=e(t);if(r instanceof Promise)return r;return{then:e=>n(e)(r),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>n(t)(e)}}},a=(e,t)=>(r,a,i)=>{let s,u={storage:function(e,t){let r;try{r=e()}catch(e){return}return{getItem:e=>{var t;let n=e=>null===e?null:JSON.parse(e,void 0),a=null!=(t=r.getItem(e))?t:null;return a instanceof Promise?a.then(n):n(a)},setItem:(e,t)=>r.setItem(e,JSON.stringify(t,void 0)),removeItem:e=>r.removeItem(e)}}(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},l=!1,o=new Set,d=new Set,c=u.storage;if(!c)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${u.name}', the given storage is currently unavailable.`),r(...e)},a,i);let g=()=>{let e=u.partialize({...a()});return c.setItem(u.name,{state:e,version:u.version})},h=i.setState;i.setState=(e,t)=>{h(e,t),g()};let m=e((...e)=>{r(...e),g()},a,i);i.getInitialState=()=>m;let f=()=>{var e,t;if(!c)return;l=!1,o.forEach(e=>{var t;return e(null!=(t=a())?t:m)});let i=(null==(t=u.onRehydrateStorage)?void 0:t.call(u,null!=(e=a())?e:m))||void 0;return n(c.getItem.bind(c))(u.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===u.version)return[!1,e.state];else{if(u.migrate){let t=u.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[n,i]=e;if(r(s=u.merge(i,null!=(t=a())?t:m),!0),n)return g()}).then(()=>{null==i||i(s,void 0),s=a(),l=!0,d.forEach(e=>e(s))}).catch(e=>{null==i||i(void 0,e)})};return i.persist={setOptions:e=>{u={...u,...e},e.storage&&(c=e.storage)},clearStorage:()=>{null==c||c.removeItem(u.name)},getOptions:()=>u,rehydrate:()=>f(),hasHydrated:()=>l,onHydrate:e=>(o.add(e),()=>{o.delete(e)}),onFinishHydration:e=>(d.add(e),()=>{d.delete(e)})},u.skipHydration||f(),s||m}}},e=>{e.O(0,[441,964,358],()=>e(e.s=6784)),_N_E=e.O()}]);