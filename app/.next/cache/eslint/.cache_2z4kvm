[{"/root/test/l-weight/app/src/app/api/admin/users/[id]/route.ts": "1", "/root/test/l-weight/app/src/app/api/admin/users/route.ts": "2", "/root/test/l-weight/app/src/app/api/auth/login/route.ts": "3", "/root/test/l-weight/app/src/app/api/auth/register/route.ts": "4", "/root/test/l-weight/app/src/app/layout.tsx": "5", "/root/test/l-weight/app/src/app/login/page.tsx": "6", "/root/test/l-weight/app/src/app/page.tsx": "7", "/root/test/l-weight/app/src/app/register/page.tsx": "8", "/root/test/l-weight/app/src/components/auth/AuthGuard.tsx": "9", "/root/test/l-weight/app/src/components/auth/LoginForm.tsx": "10", "/root/test/l-weight/app/src/components/auth/RegisterForm.tsx": "11", "/root/test/l-weight/app/src/components/ui/button.tsx": "12", "/root/test/l-weight/app/src/components/ui/card.tsx": "13", "/root/test/l-weight/app/src/components/ui/dialog.tsx": "14", "/root/test/l-weight/app/src/components/ui/dropdown-menu.tsx": "15", "/root/test/l-weight/app/src/components/ui/form.tsx": "16", "/root/test/l-weight/app/src/components/ui/input.tsx": "17", "/root/test/l-weight/app/src/components/ui/label.tsx": "18", "/root/test/l-weight/app/src/components/ui/select.tsx": "19", "/root/test/l-weight/app/src/components/ui/sonner.tsx": "20", "/root/test/l-weight/app/src/components/ui/textarea.tsx": "21", "/root/test/l-weight/app/src/lib/database.ts": "22", "/root/test/l-weight/app/src/lib/init-db.ts": "23", "/root/test/l-weight/app/src/lib/supabase.ts": "24", "/root/test/l-weight/app/src/lib/utils.ts": "25", "/root/test/l-weight/app/src/store/useAuthStore.ts": "26", "/root/test/l-weight/app/src/store/useDietStore.ts": "27", "/root/test/l-weight/app/src/types/index.ts": "28", "/root/test/l-weight/app/src/app/api/ai/analyze-calories/route.ts": "29", "/root/test/l-weight/app/src/app/api/ai/analyze-diet/route.ts": "30", "/root/test/l-weight/app/src/app/api/ai/models/route.ts": "31", "/root/test/l-weight/app/src/app/api/diet/records/[id]/route.ts": "32", "/root/test/l-weight/app/src/app/api/diet/records/route.ts": "33", "/root/test/l-weight/app/src/app/api/upload/image/route.ts": "34", "/root/test/l-weight/app/src/app/api/user/settings/route.ts": "35", "/root/test/l-weight/app/src/app/dashboard/page.tsx": "36", "/root/test/l-weight/app/src/components/dashboard/DashboardContent.tsx": "37", "/root/test/l-weight/app/src/components/diet/AnalysisResultForm.tsx": "38", "/root/test/l-weight/app/src/components/diet/DietInputForm.tsx": "39", "/root/test/l-weight/app/src/lib/ai-service.ts": "40", "/root/test/l-weight/app/src/lib/image-upload.ts": "41", "/root/test/l-weight/app/src/app/admin/page.tsx": "42", "/root/test/l-weight/app/src/components/admin/AdminDashboard.tsx": "43", "/root/test/l-weight/app/src/components/diet/DietHistory.tsx": "44", "/root/test/l-weight/app/src/components/settings/UserSettings.tsx": "45", "/root/test/l-weight/app/src/components/ui/table.tsx": "46", "/root/test/l-weight/app/src/__tests__/api.test.ts": "47"}, {"size": 4099, "mtime": 1753933085855, "results": "48", "hashOfConfig": "49"}, {"size": 3022, "mtime": 1753933124935, "results": "50", "hashOfConfig": "49"}, {"size": 1287, "mtime": 1753933098871, "results": "51", "hashOfConfig": "49"}, {"size": 1950, "mtime": 1753933112235, "results": "52", "hashOfConfig": "49"}, {"size": 794, "mtime": 1753932113092, "results": "53", "hashOfConfig": "49"}, {"size": 742, "mtime": 1753932009488, "results": "54", "hashOfConfig": "49"}, {"size": 671, "mtime": 1753932056440, "results": "55", "hashOfConfig": "49"}, {"size": 754, "mtime": 1753932022348, "results": "56", "hashOfConfig": "49"}, {"size": 1391, "mtime": 1753931993892, "results": "57", "hashOfConfig": "49"}, {"size": 3572, "mtime": 1753932305362, "results": "58", "hashOfConfig": "49"}, {"size": 4797, "mtime": 1753932335914, "results": "59", "hashOfConfig": "49"}, {"size": 2123, "mtime": 1753931611810, "results": "60", "hashOfConfig": "49"}, {"size": 1989, "mtime": 1753931611842, "results": "61", "hashOfConfig": "49"}, {"size": 3982, "mtime": 1753931611950, "results": "62", "hashOfConfig": "49"}, {"size": 8284, "mtime": 1753931611978, "results": "63", "hashOfConfig": "49"}, {"size": 3759, "mtime": 1753931612026, "results": "64", "hashOfConfig": "49"}, {"size": 967, "mtime": 1753931611858, "results": "65", "hashOfConfig": "49"}, {"size": 611, "mtime": 1753931611862, "results": "66", "hashOfConfig": "49"}, {"size": 6253, "mtime": 1753931611898, "results": "67", "hashOfConfig": "49"}, {"size": 564, "mtime": 1753931611986, "results": "68", "hashOfConfig": "49"}, {"size": 759, "mtime": 1753931611874, "results": "69", "hashOfConfig": "49"}, {"size": 6962, "mtime": 1753933221891, "results": "70", "hashOfConfig": "49"}, {"size": 2529, "mtime": 1753932352378, "results": "71", "hashOfConfig": "49"}, {"size": 1264, "mtime": 1753933179727, "results": "72", "hashOfConfig": "49"}, {"size": 166, "mtime": 1753931581925, "results": "73", "hashOfConfig": "49"}, {"size": 1188, "mtime": 1753931719482, "results": "74", "hashOfConfig": "49"}, {"size": 2196, "mtime": 1753932385090, "results": "75", "hashOfConfig": "49"}, {"size": 2356, "mtime": 1753932402458, "results": "76", "hashOfConfig": "49"}, {"size": 2062, "mtime": 1753933516473, "results": "77", "hashOfConfig": "49"}, {"size": 2225, "mtime": 1753933498401, "results": "78", "hashOfConfig": "49"}, {"size": 1468, "mtime": 1753933485497, "results": "79", "hashOfConfig": "49"}, {"size": 1008, "mtime": 1753933623286, "results": "80", "hashOfConfig": "49"}, {"size": 3480, "mtime": 1753933980932, "results": "81", "hashOfConfig": "49"}, {"size": 1443, "mtime": 1753933540241, "results": "82", "hashOfConfig": "49"}, {"size": 2271, "mtime": 1753933559161, "results": "83", "hashOfConfig": "49"}, {"size": 273, "mtime": 1753933718474, "results": "84", "hashOfConfig": "49"}, {"size": 4707, "mtime": 1753935115731, "results": "85", "hashOfConfig": "49"}, {"size": 11922, "mtime": 1753933843491, "results": "86", "hashOfConfig": "49"}, {"size": 9097, "mtime": 1753933909772, "results": "87", "hashOfConfig": "49"}, {"size": 6613, "mtime": 1753933859619, "results": "88", "hashOfConfig": "49"}, {"size": 3261, "mtime": 1753933469249, "results": "89", "hashOfConfig": "49"}, {"size": 1171, "mtime": 1753934354262, "results": "90", "hashOfConfig": "49"}, {"size": 14283, "mtime": 1753934803893, "results": "91", "hashOfConfig": "49"}, {"size": 9484, "mtime": 1753935180187, "results": "92", "hashOfConfig": "49"}, {"size": 9834, "mtime": 1753935220315, "results": "93", "hashOfConfig": "49"}, {"size": 2448, "mtime": 1753934339978, "results": "94", "hashOfConfig": "49"}, {"size": 4297, "mtime": 1753935337176, "results": "95", "hashOfConfig": "49"}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ovhm2j", {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/root/test/l-weight/app/src/app/api/admin/users/[id]/route.ts", [], [], "/root/test/l-weight/app/src/app/api/admin/users/route.ts", [], [], "/root/test/l-weight/app/src/app/api/auth/login/route.ts", [], [], "/root/test/l-weight/app/src/app/api/auth/register/route.ts", [], [], "/root/test/l-weight/app/src/app/layout.tsx", [], [], "/root/test/l-weight/app/src/app/login/page.tsx", [], [], "/root/test/l-weight/app/src/app/page.tsx", [], [], "/root/test/l-weight/app/src/app/register/page.tsx", [], [], "/root/test/l-weight/app/src/components/auth/AuthGuard.tsx", [], [], "/root/test/l-weight/app/src/components/auth/LoginForm.tsx", [], [], "/root/test/l-weight/app/src/components/auth/RegisterForm.tsx", [], [], "/root/test/l-weight/app/src/components/ui/button.tsx", [], [], "/root/test/l-weight/app/src/components/ui/card.tsx", [], [], "/root/test/l-weight/app/src/components/ui/dialog.tsx", [], [], "/root/test/l-weight/app/src/components/ui/dropdown-menu.tsx", [], [], "/root/test/l-weight/app/src/components/ui/form.tsx", [], [], "/root/test/l-weight/app/src/components/ui/input.tsx", [], [], "/root/test/l-weight/app/src/components/ui/label.tsx", [], [], "/root/test/l-weight/app/src/components/ui/select.tsx", [], [], "/root/test/l-weight/app/src/components/ui/sonner.tsx", [], [], "/root/test/l-weight/app/src/components/ui/textarea.tsx", [], [], "/root/test/l-weight/app/src/lib/database.ts", ["237"], [], "/root/test/l-weight/app/src/lib/init-db.ts", [], [], "/root/test/l-weight/app/src/lib/supabase.ts", [], [], "/root/test/l-weight/app/src/lib/utils.ts", [], [], "/root/test/l-weight/app/src/store/useAuthStore.ts", [], [], "/root/test/l-weight/app/src/store/useDietStore.ts", [], [], "/root/test/l-weight/app/src/types/index.ts", [], [], "/root/test/l-weight/app/src/app/api/ai/analyze-calories/route.ts", [], [], "/root/test/l-weight/app/src/app/api/ai/analyze-diet/route.ts", [], [], "/root/test/l-weight/app/src/app/api/ai/models/route.ts", [], [], "/root/test/l-weight/app/src/app/api/diet/records/[id]/route.ts", [], [], "/root/test/l-weight/app/src/app/api/diet/records/route.ts", [], [], "/root/test/l-weight/app/src/app/api/upload/image/route.ts", [], [], "/root/test/l-weight/app/src/app/api/user/settings/route.ts", [], [], "/root/test/l-weight/app/src/app/dashboard/page.tsx", [], [], "/root/test/l-weight/app/src/components/dashboard/DashboardContent.tsx", [], [], "/root/test/l-weight/app/src/components/diet/AnalysisResultForm.tsx", [], [], "/root/test/l-weight/app/src/components/diet/DietInputForm.tsx", [], [], "/root/test/l-weight/app/src/lib/ai-service.ts", [], [], "/root/test/l-weight/app/src/lib/image-upload.ts", [], [], "/root/test/l-weight/app/src/app/admin/page.tsx", [], [], "/root/test/l-weight/app/src/components/admin/AdminDashboard.tsx", ["238", "239", "240", "241", "242", "243", "244"], [], "/root/test/l-weight/app/src/components/diet/DietHistory.tsx", ["245"], ["246"], "/root/test/l-weight/app/src/components/settings/UserSettings.tsx", [], ["247"], "/root/test/l-weight/app/src/components/ui/table.tsx", [], [], "/root/test/l-weight/app/src/__tests__/api.test.ts", ["248"], [], {"ruleId": "249", "severity": 1, "message": "250", "line": 50, "column": 28, "nodeType": null, "messageId": "251", "endLine": 50, "endColumn": 29}, {"ruleId": "249", "severity": 1, "message": "252", "line": 15, "column": 10, "nodeType": null, "messageId": "251", "endLine": 15, "endColumn": 16}, {"ruleId": "249", "severity": 1, "message": "253", "line": 15, "column": 18, "nodeType": null, "messageId": "251", "endLine": 15, "endColumn": 31}, {"ruleId": "249", "severity": 1, "message": "254", "line": 15, "column": 33, "nodeType": null, "messageId": "251", "endLine": 15, "endColumn": 43}, {"ruleId": "249", "severity": 1, "message": "255", "line": 15, "column": 45, "nodeType": null, "messageId": "251", "endLine": 15, "endColumn": 58}, {"ruleId": "249", "severity": 1, "message": "256", "line": 15, "column": 60, "nodeType": null, "messageId": "251", "endLine": 15, "endColumn": 71}, {"ruleId": "249", "severity": 1, "message": "257", "line": 19, "column": 16, "nodeType": null, "messageId": "251", "endLine": 19, "endColumn": 20}, {"ruleId": "258", "severity": 1, "message": "259", "line": 188, "column": 6, "nodeType": "260", "endLine": 188, "endColumn": 12, "suggestions": "261"}, {"ruleId": "262", "severity": 1, "message": "263", "line": 167, "column": 23, "nodeType": "264", "endLine": 171, "endColumn": 25}, {"ruleId": "258", "severity": 1, "message": "265", "line": 95, "column": 6, "nodeType": "260", "endLine": 95, "endColumn": 12, "suggestions": "266", "suppressions": "267"}, {"ruleId": "258", "severity": 1, "message": "268", "line": 173, "column": 6, "nodeType": "260", "endLine": 173, "endColumn": 12, "suggestions": "269", "suppressions": "270"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "271", "line": 166, "column": 38}, "@typescript-eslint/no-unused-vars", "'_' is assigned a value but never used.", "unusedVar", "'Select' is defined but never used.", "'SelectContent' is defined but never used.", "'SelectItem' is defined but never used.", "'SelectTrigger' is defined but never used.", "'SelectValue' is defined but never used.", "'Edit' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", "ArrayExpression", ["272"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "React Hook useEffect has a missing dependency: 'fetchRecords'. Either include it or remove the dependency array.", ["273"], ["274"], "React Hook useEffect has a missing dependency: 'fetchSettings'. Either include it or remove the dependency array.", ["275"], ["276"], "Parsing error: Expression expected.", {"desc": "277", "fix": "278"}, {"desc": "279", "fix": "280"}, {"kind": "281", "justification": "282"}, {"desc": "283", "fix": "284"}, {"kind": "281", "justification": "282"}, "Update the dependencies array to be: [fetchUsers, user]", {"range": "285", "text": "286"}, "Update the dependencies array to be: [fetchRecords, user]", {"range": "287", "text": "288"}, "directive", "", "Update the dependencies array to be: [fetchSettings, user]", {"range": "289", "text": "290"}, [5807, 5813], "[fetchUsers, user]", [2793, 2799], "[fetchRecords, user]", [4682, 4688], "[fetchSettings, user]"]