[{"/root/test/l-weight/app/src/app/api/admin/users/[id]/route.ts": "1", "/root/test/l-weight/app/src/app/api/admin/users/route.ts": "2", "/root/test/l-weight/app/src/app/api/auth/login/route.ts": "3", "/root/test/l-weight/app/src/app/api/auth/register/route.ts": "4", "/root/test/l-weight/app/src/app/layout.tsx": "5", "/root/test/l-weight/app/src/app/login/page.tsx": "6", "/root/test/l-weight/app/src/app/page.tsx": "7", "/root/test/l-weight/app/src/app/register/page.tsx": "8", "/root/test/l-weight/app/src/components/auth/AuthGuard.tsx": "9", "/root/test/l-weight/app/src/components/auth/LoginForm.tsx": "10", "/root/test/l-weight/app/src/components/auth/RegisterForm.tsx": "11", "/root/test/l-weight/app/src/components/ui/button.tsx": "12", "/root/test/l-weight/app/src/components/ui/card.tsx": "13", "/root/test/l-weight/app/src/components/ui/dialog.tsx": "14", "/root/test/l-weight/app/src/components/ui/dropdown-menu.tsx": "15", "/root/test/l-weight/app/src/components/ui/form.tsx": "16", "/root/test/l-weight/app/src/components/ui/input.tsx": "17", "/root/test/l-weight/app/src/components/ui/label.tsx": "18", "/root/test/l-weight/app/src/components/ui/select.tsx": "19", "/root/test/l-weight/app/src/components/ui/sonner.tsx": "20", "/root/test/l-weight/app/src/components/ui/textarea.tsx": "21", "/root/test/l-weight/app/src/lib/database.ts": "22", "/root/test/l-weight/app/src/lib/init-db.ts": "23", "/root/test/l-weight/app/src/lib/supabase.ts": "24", "/root/test/l-weight/app/src/lib/utils.ts": "25", "/root/test/l-weight/app/src/store/useAuthStore.ts": "26", "/root/test/l-weight/app/src/store/useDietStore.ts": "27", "/root/test/l-weight/app/src/types/index.ts": "28"}, {"size": 4186, "mtime": 1753932679088, "results": "29", "hashOfConfig": "30"}, {"size": 3218, "mtime": 1753932479707, "results": "31", "hashOfConfig": "30"}, {"size": 1283, "mtime": 1753932206429, "results": "32", "hashOfConfig": "30"}, {"size": 2033, "mtime": 1753932258749, "results": "33", "hashOfConfig": "30"}, {"size": 794, "mtime": 1753932113092, "results": "34", "hashOfConfig": "30"}, {"size": 742, "mtime": 1753932009488, "results": "35", "hashOfConfig": "30"}, {"size": 671, "mtime": 1753932056440, "results": "36", "hashOfConfig": "30"}, {"size": 754, "mtime": 1753932022348, "results": "37", "hashOfConfig": "30"}, {"size": 1391, "mtime": 1753931993892, "results": "38", "hashOfConfig": "30"}, {"size": 3572, "mtime": 1753932305362, "results": "39", "hashOfConfig": "30"}, {"size": 4797, "mtime": 1753932335914, "results": "40", "hashOfConfig": "30"}, {"size": 2123, "mtime": 1753931611810, "results": "41", "hashOfConfig": "30"}, {"size": 1989, "mtime": 1753931611842, "results": "42", "hashOfConfig": "30"}, {"size": 3982, "mtime": 1753931611950, "results": "43", "hashOfConfig": "30"}, {"size": 8284, "mtime": 1753931611978, "results": "44", "hashOfConfig": "30"}, {"size": 3759, "mtime": 1753931612026, "results": "45", "hashOfConfig": "30"}, {"size": 967, "mtime": 1753931611858, "results": "46", "hashOfConfig": "30"}, {"size": 611, "mtime": 1753931611862, "results": "47", "hashOfConfig": "30"}, {"size": 6253, "mtime": 1753931611898, "results": "48", "hashOfConfig": "30"}, {"size": 564, "mtime": 1753931611986, "results": "49", "hashOfConfig": "30"}, {"size": 759, "mtime": 1753931611874, "results": "50", "hashOfConfig": "30"}, {"size": 6493, "mtime": 1753931813583, "results": "51", "hashOfConfig": "30"}, {"size": 2529, "mtime": 1753932352378, "results": "52", "hashOfConfig": "30"}, {"size": 723, "mtime": 1753931702170, "results": "53", "hashOfConfig": "30"}, {"size": 166, "mtime": 1753931581925, "results": "54", "hashOfConfig": "30"}, {"size": 1188, "mtime": 1753931719482, "results": "55", "hashOfConfig": "30"}, {"size": 2196, "mtime": 1753932385090, "results": "56", "hashOfConfig": "30"}, {"size": 2356, "mtime": 1753932402458, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1ovhm2j", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/root/test/l-weight/app/src/app/api/admin/users/[id]/route.ts", ["142"], [], "/root/test/l-weight/app/src/app/api/admin/users/route.ts", ["143", "144"], [], "/root/test/l-weight/app/src/app/api/auth/login/route.ts", ["145"], [], "/root/test/l-weight/app/src/app/api/auth/register/route.ts", ["146"], [], "/root/test/l-weight/app/src/app/layout.tsx", [], [], "/root/test/l-weight/app/src/app/login/page.tsx", [], [], "/root/test/l-weight/app/src/app/page.tsx", [], [], "/root/test/l-weight/app/src/app/register/page.tsx", [], [], "/root/test/l-weight/app/src/components/auth/AuthGuard.tsx", [], [], "/root/test/l-weight/app/src/components/auth/LoginForm.tsx", [], [], "/root/test/l-weight/app/src/components/auth/RegisterForm.tsx", [], [], "/root/test/l-weight/app/src/components/ui/button.tsx", [], [], "/root/test/l-weight/app/src/components/ui/card.tsx", [], [], "/root/test/l-weight/app/src/components/ui/dialog.tsx", [], [], "/root/test/l-weight/app/src/components/ui/dropdown-menu.tsx", [], [], "/root/test/l-weight/app/src/components/ui/form.tsx", [], [], "/root/test/l-weight/app/src/components/ui/input.tsx", [], [], "/root/test/l-weight/app/src/components/ui/label.tsx", [], [], "/root/test/l-weight/app/src/components/ui/select.tsx", [], [], "/root/test/l-weight/app/src/components/ui/sonner.tsx", [], [], "/root/test/l-weight/app/src/components/ui/textarea.tsx", [], [], "/root/test/l-weight/app/src/lib/database.ts", [], [], "/root/test/l-weight/app/src/lib/init-db.ts", [], [], "/root/test/l-weight/app/src/lib/supabase.ts", [], [], "/root/test/l-weight/app/src/lib/utils.ts", [], [], "/root/test/l-weight/app/src/store/useAuthStore.ts", [], [], "/root/test/l-weight/app/src/store/useDietStore.ts", [], [], "/root/test/l-weight/app/src/types/index.ts", [], [], {"ruleId": "147", "severity": 1, "message": "148", "line": 55, "column": 28, "nodeType": null, "messageId": "149", "endLine": 55, "endColumn": 29}, {"ruleId": "147", "severity": 1, "message": "150", "line": 39, "column": 51, "nodeType": null, "messageId": "149", "endLine": 39, "endColumn": 52}, {"ruleId": "147", "severity": 1, "message": "148", "line": 80, "column": 28, "nodeType": null, "messageId": "149", "endLine": 80, "endColumn": 29}, {"ruleId": "147", "severity": 1, "message": "148", "line": 28, "column": 28, "nodeType": null, "messageId": "149", "endLine": 28, "endColumn": 29}, {"ruleId": "147", "severity": 1, "message": "148", "line": 31, "column": 28, "nodeType": null, "messageId": "149", "endLine": 31, "endColumn": 29}, "@typescript-eslint/no-unused-vars", "'_' is assigned a value but never used.", "unusedVar", "'_' is defined but never used."]